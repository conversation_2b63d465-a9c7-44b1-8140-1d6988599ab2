{"name": "yili-act-b", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "test": "vite serve --mode test", "prod": "vite serve --mode production", "build": "vue-tsc && vite build", "preview": "vite preview", "serve:mock": "vite --mode mock", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:prod": "vite build --mode production"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@antv/g2": "^5.1.15", "@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.2.0", "ant-design-vue": "^3.2.20", "axios": "^1.4.0", "colorpicker-v3": "^2.10.2", "cross-env": "^7.0.3", "echarts": "^5.4.3", "element-plus": "^2.3.7", "events": "^3.3.0", "file-saver": "^2.0.5", "mockjs": "^1.1.0", "moment": "^2.29.4", "pinia": "^2.1.1", "pinia-plugin-persist": "^1.0.0", "qrcodejs2-fix": "^0.0.1", "swiper": "^6.7.0", "vite-plugin-mock": "^3.0.0", "vue": "^3.3.2", "vue-json-excel": "^0.3.0", "vue-router": "4", "vuedraggable": "^2.24.3"}, "devDependencies": {"@types/node": "^20.2.5", "@types/swiper": "^5.4.3", "@vitejs/plugin-vue": "^4.1.0", "autoprefixer": "^10.4.14", "http-proxy-middleware": "^2.0.6", "less": "^4.1.3", "postcss": "^8.4.24", "prettier": "^2.8.8", "sass": "^1.63.6", "sass-loader": "^13.3.2", "typescript": "^5.0.2", "unplugin-vue-components": "^0.25.0", "vite": "^4.3.9", "vue-tsc": "^1.4.2", "xlsx": "^0.18.5"}}