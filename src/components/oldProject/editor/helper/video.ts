import { VideoElement } from "@wangeditor/editor";
import { baseURL } from "~/utils/http";
import useUserStore from "~/store/user";

const userStore = useUserStore();

const headers = {
  AccessToken: userStore.accessToken,
};

// 自定义校验视频
function customCheckVideoFn(src: string): boolean | string | undefined {
  if (!src) {
    return;
  }
  if (src.indexOf("http") !== 0) {
    return "视频地址必须以 http/https 开头";
  }
  return true;
}

// 自定义转换视频
function customParseVideoSrc(src: string): string {
  return src;
}

const helper = {
  uploadVideo: {
    server: `${baseURL}/upload/video`,

    headers: headers,

    // form-data fieldName ，默认值 'wangeditor-uploaded-image'
    fieldName: "file",

    // 单个文件的最大体积限制，默认为 2M
    maxFileSize: 50 * 1024 * 1024, // 50M

    // 最多可上传几个文件，默认为 100
    maxNumberOfFiles: 10,

    // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
    allowedFileTypes: ["video/*"],

    // 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
    //   meta: {
    //     token: "xxx",
    //     otherKey: "yyy",
    //   },

    // 将 meta 拼接到 url 参数中，默认 false
    metaWithUrl: false,

    // 自定义增加 http  header
    //   headers: {
    //     Accept: "text/x-json",
    //     otherKey: "xxx",
    //   },

    // 跨域是否传递 cookie ，默认为 false
    withCredentials: false,

    // 超时时间，默认为 10 秒
    timeout: 5 * 1000, // 5 秒
  },
  // 插入图片
  insertVideo: {
    onInsertedVideo(videoNode: VideoElement | null) {
      if (videoNode == null) return;

      const { src } = videoNode;
      console.log("inserted video", src);
    },
    checkVideo: customCheckVideoFn, // 也支持 async 函数
    parseVideoSrc: customParseVideoSrc, // 也支持 async 函数
  },
};

export default helper;
