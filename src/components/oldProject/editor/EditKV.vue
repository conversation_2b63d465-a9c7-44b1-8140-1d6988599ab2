<template>
	<div class="edit-page-kv">
		<div class="toptitle">
			*
			<p class="toptitle1">KV区图片</p>
		</div>
		<el-upload
			class="image-uploader1"
			list-type="picture-card"
			:file-list="fileList"
			:accept="accept.join(',')"
			:on-preview="handlePictureCardPreview"
			action=""
			:before-upload="handleBeforeUpload"
			:http-request="handleAvatarRankSuccess"
			v-loading="isLoading"
			:on-success="handleUploadSuccess"
			:on-remove="handleRemove"
		>
			<el-space direction="vertical">
				<span class="centerimg"></span>
				<p class="centerp">请上传图片</p>
			</el-space>
		</el-upload>
		<p class="toptitle1">图片尺寸：仅支持宽度为750PX，高度450PX</p>
		<p class="toptitle1">图片大小：大小不超过2M</p>
		<p class="toptitle1">图片类型：JPG、JPEG、PNG</p>
		<div class="toptitle">
			*
			<p class="toptitle1">跳转链接</p>
		</div>
		<el-input class="btmtext" v-model="toUrl" placeholder="请输入链接" @blur="inputchange(1)"></el-input>
		<div class="edit-page-kvdefault">
			<div class="toptitle">
				*
				<p class="toptitle1">KV区图片</p>
			</div>
			<el-upload
				class="image-uploader1"
				list-type="picture-card"
				:file-list="fileList1"
				:accept="accept.join(',')"
				:on-preview="handlePictureCardPreview"
				action=""
				:before-upload="handleBeforeUpload"
				:http-request="handleAvatarRankSuccess"
				v-loading="isLoading"
				:on-success="handleUploadSuccess"
				:on-remove="handleRemove"
			>
				<el-space direction="vertical">
					<span class="centerimg"></span>
					<p class="centerp">请上传图片</p>
				</el-space>
			</el-upload>
			<p class="toptitle1">图片尺寸：仅支持宽度为750PX，高度450PX</p>
			<p class="toptitle1">图片大小：大小不超过2M</p>
			<p class="toptitle1">图片类型：JPG、JPEG、PNG</p>
			<div class="toptitle">
				*
				<p class="toptitle1">跳转链接</p>
			</div>
			<el-input class="btmtext" v-model="toUrl1" placeholder="请输入链接" @blur="inputchange(2)"></el-input>
		</div>
		<div class="edit-page-kvdefault">
			<div class="toptitle">
				*
				<p class="toptitle1">KV区图片</p>
			</div>
			<el-upload
				class="image-uploader1"
				list-type="picture-card"
				:file-list="fileList2"
				:accept="accept.join(',')"
				:on-preview="handlePictureCardPreview"
				action=""
				:before-upload="handleBeforeUpload"
				:http-request="handleAvatarRankSuccess"
				v-loading="isLoading"
				:on-success="handleUploadSuccess"
				:on-remove="handleRemove"
			>
				<el-space direction="vertical">
					<span class="centerimg"></span>
					<p class="centerp">请上传图片</p>
				</el-space>
			</el-upload>
			<p class="toptitle1">图片尺寸：仅支持宽度为750PX，高度450PX</p>
			<p class="toptitle1">图片大小：大小不超过2M</p>
			<p class="toptitle1">图片类型：JPG、JPEG、PNG</p>
			<div class="toptitle">
				*
				<p class="toptitle1">跳转链接</p>
			</div>
			<el-input class="btmtext" v-model="toUrl2" placeholder="请输入链接" @blur="inputchange(3)"></el-input>
		</div>
	</div>
	<el-dialog v-model="dialogVisible" title="图片预览">
		<img :src="dialogImageUrl" style="max-width: 100%; margin: auto; display: inherit" />
	</el-dialog>
</template>
<script lang="ts" setup>
import { imageProps, UploadFile, UploadFiles, UploadInstance, UploadProps, UploadRawFile, UploadUserFile } from 'element-plus';
import { ElMessage, genFileId } from 'element-plus';
import _, { size } from 'lodash';
import { getImgUrlBase64 } from '/@/utils/request/api/yili/api';
import { UploadResponse } from 'types/http';
import { defineProps, ref, defineExpose, defineEmits, Ref, onMounted, reactive } from 'vue';

const props = defineProps({
	model1: {
		type: [] as any,
		default: null
	}
});
onMounted(() => {
	if (props.model1.data[0] && props.model1.data[0].imgUrl != '') {
		toUrl.value = props.model1.data[0].jumpUrl;
		var all = {
			name: props.model1.data[0].name,
			url: props.model1.data[0].imgUrl
		};
		fileList.value.push(all);
		saveimg.value.push(props.model1.data[0]);
	}
	if (props.model1.data[1] && props.model1.data[1].imgUrl != '') {
		toUrl1.value = props.model1.data[1].jumpUrl;
		var all1 = {
			name: props.model1.data[1].name,
			url: props.model1.data[1].imgUrl
		};
		fileList1.value.push(all1);
		saveimg.value.push(props.model1.data[1]);
	}
	if (props.model1.data[2] && props.model1.data[2].imgUrl != '') {
		toUrl2.value = props.model1.data[2].jumpUrl;
		var all2 = {
			name: props.model1.data[2].name,
			url: props.model1.data[2].imgUrl
		};
		fileList2.value.push(all2);
		saveimg.value.push(props.model1.data[2]);
	}
	console.log(props.model1.data);
});
const toUrl = ref('');
const toUrl1 = ref('');
const toUrl2 = ref('');

const emit = defineEmits(['getSaveImg', 'removeSaveImg', 'getSaveToUrl']);

const dialogVisible = ref(false);
const dialogImageUrl = ref('');
// 预览图片的时候触发
const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
	console.log('handlePictureCardPreview', uploadFile);
	dialogImageUrl.value = uploadFile.url!;
	dialogVisible.value = true;
};
// 上传头部信息
const fileList = ref<UploadUserFile[]>([]);
const fileList1 = ref<UploadUserFile[]>([]);
const fileList2 = ref<UploadUserFile[]>([]);
const uploader = ref<UploadInstance>();
const accept = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];

const isLoading = ref(false);
const saveimg = ref([] as any);
const handleAvatarRankSuccess = async (file: any) => {
	const imgflag = ref(false);
	let formData = new FormData();
	formData.append('file', file.file);
	// isLoading.value = true;
	const objimg = reactive({
		uid: file.file.uid,
		name: file.file.name,
		imgUrl: ''
	});

	const res = await getImgUrlBase64(formData);
	objimg.imgUrl = res;
	saveimg.value.forEach((item: any, index: number) => {
		if (item == '') {
			saveimg.value[index] = objimg;
			imgflag.value = true;
		}
	});
	if (!imgflag.value) {
		saveimg.value.push(objimg);
	}
	emit('getSaveImg', saveimg.value, 'KV');
};

const inputchange = (index: number) => {
	const newValue = index == 1 ? toUrl.value : index == 2 ? toUrl1.value : toUrl2.value;
	emit('getSaveToUrl', newValue, 'KV', index);
};

// 移除图片的时候触发
const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
	console.log('handleRemove uploadFile', uploadFile);
	console.log('handleRemove uploadFiles ', uploadFiles);
	saveimg.value.forEach((item: any, index: number) => {
		if (item.imgUrl == uploadFile.url) {
			switch (index) {
				case 0:
					toUrl.value = '';
					break;
				case 1:
					toUrl1.value = '';
					break;
				case 2:
					toUrl2.value = '';
					break;
			}
			saveimg.value[index] = '';
			inputchange(index);
			emit('removeSaveImg', index, 'KV');
		}
	});
};
// 上传成功的时候触发
const handleUploadSuccess = (response: UploadResponse, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
	console.log('handleUploadSuccess', response, uploadFile, uploadFiles);
};

// 选择图片，移除图片的时候触发
const handleChange: UploadProps['onChange'] = (uploadFile) => {
	console.log('onChange', uploadFile);
};

import { imgSizeCheck } from '/@/utils/imgcheck';
let removeimg = false;
// 上传之前触发
const handleBeforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
	return new Promise((resolve: any, reject: any) => {
		if (!accept.includes(rawFile.type)) {
			ElMessage.error('不支持的图片格式');
			return reject(false);
		}
		if (rawFile.size / 1024 / 1024 > 2) {
			ElMessage.error('图片大小不能超过 2MB!');
			return reject(false);
		}
		console.log('上传之前');
		console.log(rawFile.size / 1024 / 1024 > 2);
		console.log(!accept.includes(rawFile.type));
		// 校验图片尺寸
		imgSizeCheck(rawFile, 750, 450).then((res) => {
			console.log(res);
			if (!res) {
				removeimg = false;
				return reject(false);
			} else {
				removeimg = true;
				return resolve(true);
			}
		});
	});
};
</script>
<style lang="scss" scoped>
.edit-page-kv::-webkit-scrollbar {
	display: none;
}
.edit-page-kv {
	width: 360px;
	height: 63vh;
	border-radius: 0px 0px 0px 0px;
	border: 1px solid #d7dde4;
	margin: 20px;
	padding: 15px;
	overflow-y: scroll;
	overflow-x: hidden;
	.image-uploader1 {
		height: 180px;
		overflow: hidden;
		.centerimg {
			width: 40px;
			height: 36px;
			background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/156195/4/29925/876/6315ae46E1bf68910/6414178d96316c3a.png');
			background-size: 100%;
		}
		.centerp {
			width: 60px;
			height: 12px;
			font-size: 12px;
			font-family: PingFang SC-常规体, PingFang SC;
			font-weight: normal;
			color: #9ca7b5;
			line-height: 12px;
			-webkit-background-clip: text;
		}
		&.isFull {
			:deep(.el-upload-list--picture-card .el-upload--picture-card) {
				display: none;
			}
		}

		:deep(.el-icon--close-tip) {
			display: none !important;
		}

		:deep(.el-upload-list--picture-card .el-upload-list__item) {
			// @include uploadSize;
			width: 330px;
			height: 177px;
		}

		:deep(.el-upload--picture-card) {
			// @include uploadSize;
			width: 330px;
			height: 177px;
		}
	}
	.edit-page-kvdefault {
		width: 330px;
		height: 357px;
		background: #f9f9f9;
		margin-bottom: 20px;
	}
	.toptitle {
		display: flex;
		flex-direction: row;
		align-items: center;
		color: red;
	}
	.toptitle1 {
		width: auto;
		height: 9px;
		font-size: 12px;
		font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 6px;
		-webkit-background-clip: text;
	}
	.btmtext {
		width: 329px;
		height: 30px;
		margin-bottom: 20px;
	}
}
// .image-uploader{
//   margin: 0px 20px 20px 20px;
//     .btmp{
//       width: auto;
//       height: 12px;
//       font-size: 12px;
//       font-family: PingFang SC-Regular, PingFang SC;
//       font-weight: 400;
//       color: #3399FF;
//       line-height: 12px;
//       -webkit-background-clip: text;
//     }
//     &.isFull {
//       :deep(.el-upload-list--picture-card .el-upload--picture-card) {
//         display: none;
//       }
//     }

//     :deep(.el-icon--close-tip) {
//       display: none !important;
//     }

//     :deep(.el-upload-list--picture-card .el-upload-list__item) {
//       width: 360px;
//       height: 32px;
//     }

//     :deep(.el-upload--picture-card) {
//       width: 360px;
//       height: 32px;
//     }
// }
.gray100 {
	filter: grayscale(100%);
}
</style>
