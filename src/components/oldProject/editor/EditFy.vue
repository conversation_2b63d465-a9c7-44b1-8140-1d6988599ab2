<template>
	<div class="edit-page-kv">
		<div class="toptitle">
			*
			<p class="toptitle1">模块背景</p>
		</div>
		<lz-image-uploader
			@getbtmSaveImg="getbtmSaveImg"
			@removebtmSaveImg="removebtmSaveImg"
			:width="750"
			:height="983"
			:fileList="fileList"
			@uploadimg="uploadimg"
			:aspect-ratio="1 / 2"
			:limit="1"
		/>
		<p class="toptitle1">图片尺寸：仅支持宽度为750PX，高度983PX</p>
		<p class="toptitle1">图片大小：大小不超过2M</p>
		<p class="toptitle1">图片类型：JPG、JPEG、PNG</p>
		<div class="toptitle">
			*
			<p class="toptitle1">添加内容:</p>
		</div>
		<div class="edit-page-fy">
			<div class="toptitle">
				*
				<p class="toptitle1">海报图片1:</p>
			</div>
			<lz-image-uploader
				@getbtmSaveImg="getbtmSaveImg1"
				@removebtmSaveImg="removebtmSaveImg1"
				:width="660"
				:height="165"
				:imgloaddisabled="filedisabled"
				:fileList="fileList1"
				@uploadimg="uploadimg1"
				:aspect-ratio="1 / 2"
				:limit="1"
			/>
			<p class="toptitle1">图片尺寸：仅支持宽度为660PX，高度165PX</p>
			<p class="toptitle1">图片大小：大小不超过2M</p>
			<p class="toptitle1">图片类型：JPG、JPEG、PNG</p>
			<div class="toptitle">
				*
				<p class="toptitle1">跳转链接</p>
			</div>
			<el-input class="btmtext" v-model="toUrl1" placeholder="请输入链接"></el-input>
		</div>
		<div class="edit-page-fy">
			<div class="toptitle">
				*
				<p class="toptitle1">海报图片2:</p>
			</div>
			<lz-image-uploader
				@getbtmSaveImg="getbtmSaveImg2"
				@removebtmSaveImg="removebtmSaveImg2"
				:width="660"
				:height="165"
				:imgloaddisabled="filedisabled"
				:fileList="fileList2"
				@uploadimg="uploadimg2"
				:aspect-ratio="1 / 2"
				:limit="1"
			/>
			<p class="toptitle1">图片尺寸：仅支持宽度为660PX，高度165PX</p>
			<p class="toptitle1">图片大小：大小不超过2M</p>
			<p class="toptitle1">图片类型：JPG、JPEG、PNG</p>
			<div class="toptitle">
				*
				<p class="toptitle1">跳转链接</p>
			</div>
			<el-input class="btmtext" v-model="toUrl2" placeholder="请输入链接"></el-input>
		</div>
		<div class="edit-page-fy">
			<div class="toptitle">
				*
				<p class="toptitle1">海报图片3:</p>
			</div>
			<lz-image-uploader
				@getbtmSaveImg="getbtmSaveImg3"
				@removebtmSaveImg="removebtmSaveImg3"
				:width="660"
				:height="165"
				:imgloaddisabled="filedisabled"
				:fileList="fileList3"
				@uploadimg="uploadimg3"
				:aspect-ratio="1 / 2"
				:limit="1"
			/>
			<p class="toptitle1">图片尺寸：仅支持宽度为660PX，高度165PX</p>
			<p class="toptitle1">图片大小：大小不超过2M</p>
			<p class="toptitle1">图片类型：JPG、JPEG、PNG</p>
			<div class="toptitle">
				*
				<p class="toptitle1">跳转链接</p>
			</div>
			<el-input class="btmtext" v-model="toUrl3" placeholder="请输入链接"></el-input>
		</div>
		<div class="edit-page-fy">
			<div class="toptitle">
				*
				<p class="toptitle1">海报图片4:</p>
			</div>
			<lz-image-uploader
				@getbtmSaveImg="getbtmSaveImg4"
				@removebtmSaveImg="removebtmSaveImg4"
				:width="660"
				:height="165"
				:imgloaddisabled="filedisabled"
				:fileList="fileList4"
				@uploadimg="uploadimg4"
				:aspect-ratio="1 / 2"
				:limit="1"
			/>
			<p class="toptitle1">图片尺寸：仅支持宽度为660PX，高度165PX</p>
			<p class="toptitle1">图片大小：大小不超过2M</p>
			<p class="toptitle1">图片类型：JPG、JPEG、PNG</p>
			<div class="toptitle">
				*
				<p class="toptitle1">跳转链接</p>
			</div>
			<el-input class="btmtext" v-model="toUrl4" placeholder="请输入链接"></el-input>
		</div>
	</div>
</template>
<script lang="ts" setup>
import type { UploadFile, UploadFiles, UploadInstance, UploadProps, UploadRawFile, UploadUserFile } from 'element-plus';
import { ElMessage, genFileId } from 'element-plus';
import _ from 'lodash';
import { UploadResponse } from 'types/http';
import { ref, defineExpose, defineEmits, onMounted, watch } from 'vue';
import { getImgUrlBase64 } from '/@/utils/request/api/yili/api';
const props = defineProps({
	model4: {
		type: [] as any,
		default: null
	}
});
onMounted(() => {
	if (props.model4.data[0] && props.model4.data[0].imgUrl != '') {
		var all = {
			name: props.model4.data[0].name,
			url: props.model4.data[0].imgUrl
		};
		fileList.value.push(all);
		filedisabled.value = false;
		saveimg.value.push(props.model4.data[0]);
	}
	if (props.model4.data[1] && props.model4.data[1].imgUrl != '') {
		toUrl1.value = props.model4.data[1].jumpUrl;
		var all = {
			name: props.model4.data[1].name,
			url: props.model4.data[1].imgUrl
		};
		fileList1.value.push(all);
		saveimg.value.push(props.model4.data[1]);
	}
	if (props.model4.data[2] && props.model4.data[2].imgUrl != '') {
		toUrl2.value = props.model4.data[2].jumpUrl;
		var all = {
			name: props.model4.data[2].name,
			url: props.model4.data[2].imgUrl
		};
		fileList2.value.push(all);
		saveimg.value.push(props.model4.data[2]);
	}
	if (props.model4.data[3] && props.model4.data[3].imgUrl != '') {
		toUrl3.value = props.model4.data[3].jumpUrl;
		var all = {
			name: props.model4.data[3].name,
			url: props.model4.data[3].imgUrl
		};
		fileList3.value.push(all);
		saveimg.value.push(props.model4.data[3]);
	}
	if (props.model4.data[4] && props.model4.data[4].imgUrl != '') {
		toUrl4.value = props.model4.data[4].jumpUrl;
		var all = {
			name: props.model4.data[4].name,
			url: props.model4.data[4].imgUrl
		};
		fileList4.value.push(all);
		saveimg.value.push(props.model4.data[4]);
	}
});
const toUrl1 = ref('');
const toUrl2 = ref('');
const toUrl3 = ref('');
const toUrl4 = ref('');

const emit = defineEmits(['getSaveImg', 'removeSaveImg', 'getSaveToUrl']);

// 上传头部信息
const fileList = ref<UploadUserFile[]>([]);
const fileList1 = ref<UploadUserFile[]>([]);
const fileList2 = ref<UploadUserFile[]>([]);
const fileList3 = ref<UploadUserFile[]>([]);
const fileList4 = ref<UploadUserFile[]>([]);
const uploader = ref<UploadInstance>();
const accept = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];

const isLoading = ref(false);
const saveimg = ref([] as any);
const handleAvatarRankSuccess = async (file: any) => {
	let formData = new FormData();
	formData.append('file', file.file);
	// isLoading.value = true;
	const res = await getImgUrlBase64(formData);
	saveimg.value.push(res);
	emit('getSaveImg', saveimg.value, '伊起复约，好礼不停');
};

const getbtmSaveImg = (val: string) => {
	emit('getSaveImg', val, '伊起复约，好礼不停', 0);
};
const getbtmSaveImg1 = (val: string) => {
	emit('getSaveImg', val, '伊起复约，好礼不停', 1);
};
const getbtmSaveImg2 = (val: string) => {
	emit('getSaveImg', val, '伊起复约，好礼不停', 2);
};
const getbtmSaveImg3 = (val: string) => {
	emit('getSaveImg', val, '伊起复约，好礼不停', 3);
};
const getbtmSaveImg4 = (val: string) => {
	emit('getSaveImg', val, '伊起复约，好礼不停', 4);
};
const removebtmSaveImg = (uploadFile: any, uploadFiles: any) => {
	emit('removeSaveImg', 0, '伊起复约，好礼不停');
};
const removebtmSaveImg1 = (uploadFile: any, uploadFiles: any) => {
	toUrl1.value = '';
	emit('removeSaveImg', 1, '伊起复约，好礼不停');
};
const removebtmSaveImg2 = (uploadFile: any, uploadFiles: any) => {
	toUrl2.value = '';
	emit('removeSaveImg', 2, '伊起复约，好礼不停');
};
const removebtmSaveImg3 = (uploadFile: any, uploadFiles: any) => {
	toUrl3.value = '';
	emit('removeSaveImg', 3, '伊起复约，好礼不停');
};
const removebtmSaveImg4 = (uploadFile: any, uploadFiles: any) => {
	toUrl4.value = '';
	emit('removeSaveImg', 4, '伊起复约，好礼不停');
};

const filedisabled = ref(true);
watch([() => toUrl1.value, () => toUrl2.value, () => toUrl3.value, () => toUrl4.value], (newValue) => {
	emit('getSaveToUrl', newValue, '伊起复约，好礼不停');
});

const uploadimg = (val: any) => {
	filedisabled.value = val ? false : true;
	fileList.value.push(val[0]);
};
const uploadimg1 = (val: any) => {
	fileList1.value.push(val[0]);
};
const uploadimg2 = (val: any) => {
	fileList2.value.push(val[0]);
};
const uploadimg3 = (val: any) => {
	fileList3.value.push(val[0]);
};
const uploadimg4 = (val: any) => {
	fileList4.value.push(val[0]);
};

// 移除图片的时候触发
const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
	console.log('handleRemove uploadFile', uploadFile);
	console.log('handleRemove uploadFiles ', uploadFiles);
};
// 上传成功的时候触发
const handleUploadSuccess = (response: UploadResponse, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
	console.log('handleUploadSuccess', response, uploadFile, uploadFiles);
	// doEmit(uploadFiles);
};

// 选择图片，移除图片的时候触发
const handleChange: UploadProps['onChange'] = (uploadFile) => {
	console.log('onChange', uploadFile);
};

// 上传之前触发
const handleBeforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
	if (fileList1.value.length >= 4) {
		ElMessage.error('添加图片不可超过4张');
		return false;
	}
	if (!accept.includes(rawFile.type)) {
		ElMessage.error('不支持的图片格式');
		return false;
	}
	if (rawFile.size / 1024 / 1024 > 2) {
		ElMessage.error('图片尺寸不能超过 2MB!');
		return false;
	}
	return true;
};
</script>
<style lang="scss" scoped>
.edit-page-kv::-webkit-scrollbar {
	display: none;
}
.edit-page-kv {
	width: 360px;
	height: 63vh;
	border-radius: 0px 0px 0px 0px;
	border: 1px solid #d7dde4;
	margin: 20px;
	padding: 15px;
	overflow-y: scroll;
	overflow-x: hidden;
	.toptitle {
		display: flex;
		flex-direction: row;
		align-items: center;
		color: red;
	}
	.toptitle1 {
		width: auto;
		height: 9px;
		font-size: 12px;
		font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 6px;
		-webkit-background-clip: text;
	}
	.btmtext {
		width: 329px;
		height: 30px;
	}
}

.edit-page-fy {
	width: 330px;
	height: 365px;
	background: #f9f9f9;
	margin-bottom: 20px;
	.image-uploader {
		margin: 15px 0;
		.btmp {
			width: auto;
			height: 12px;
			font-size: 12px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #3399ff;
			line-height: 12px;
			-webkit-background-clip: text;
		}
		&.isFull {
			:deep(.el-upload-list--picture-card .el-upload--picture-card) {
				display: none;
			}
		}

		:deep(.el-icon--close-tip) {
			display: none !important;
		}

		:deep(.el-upload-list--picture-card .el-upload-list__item) {
			width: 330px;
			height: 32px;
		}

		:deep(.el-upload--picture-card) {
			width: 330px;
			height: 32px;
		}
	}
}
.gray100 {
	filter: grayscale(100%);
}
</style>
