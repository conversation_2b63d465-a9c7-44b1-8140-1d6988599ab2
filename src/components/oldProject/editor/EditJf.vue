<template>
	<div id="pagekv" class="edit-page-kv">
		<div class="toptitle">
			*
			<p class="toptitle1">模块背景</p>
		</div>
		<lz-image-uploader
			@getbtmSaveImg="getbtmSaveImg"
			@removebtmSaveImg="removebtmSaveImg"
			:width="750"
			:height="758"
			:fileList="fileList"
			@uploadimg="uploadimg"
			:aspect-ratio="1 / 2"
			:limit="1"
		/>
		<p class="toptitle1">图片尺寸：仅支持宽度为750PX，高度758PX</p>
		<p class="toptitle1">图片大小：大小不超过2M</p>
		<p class="toptitle1">图片类型：JPG、JPEG、PNG</p>
		<div class="toptitle">
			*
			<p class="toptitle1">积分兑换活动:</p>
		</div>
		<div class="btmbg">
			<el-radio style="margin-right: 20px" v-model="pointTactics" label="1">使用推荐策略</el-radio>
			<el-radio v-model="pointTactics" label="2">不使用推荐策略（按顺序展示）</el-radio>
		</div>
		<div class="page-title-content">
			<el-icon class="icon-zhuyi"></el-icon>
			<p class="p-zhuyi" v-html="zhuYiFont"></p>
		</div>
		<div v-for="(item, index) in uploadFileList" :lable="index" v-show="modeljfnum >= index" :key="index" class="edit-page-jf modeljfclass">
			<div class="topbg">
				<el-upload
					:disabled="filedisabled"
					class="image-uploader1"
					list-type="picture-card"
					:file-list="item.pics"
					:accept="accept.join(',')"
					:on-preview="handlePictureCardPreview"
					action=""
					:before-upload="handleBeforeUpload"
					:http-request="handleAvatarRankSuccess"
					v-loading="isLoading"
					:on-success="handleUploadSuccess"
					:on-remove="handleRemove"
				>
					<p class="btmp" :class="{ gray100: filedisabled }">+积分兑换图片</p>
				</el-upload>
				<div class="rightbg">
					<div class="rightbg-div1">
						<input :disabled="filedisabled" type="number" @blur="inputchange" class="input1" />
						<p>积分可兑</p>
					</div>
					<div class="rightbg-div2">
						<p>点击后跳转:</p>
						<input :disabled="filedisabled" @blur="inputchange" class="input2" />
					</div>
				</div>
			</div>
			<p class="toptitle1">图片尺寸：仅支持宽度为190PX，高度240PX</p>
			<p class="toptitle1">图片大小：大小不超过2M</p>
			<p class="toptitle1">图片类型：JPG、JPEG、PNG</p>
		</div>
		<el-button v-if="activityPage === 1" @click="addmodel" :disabled="filedisabled" class="addmodel1" :class="{ gray100: filedisabled }">+添加图片</el-button>
		<el-button v-else @click="addmodel" :disabled="filedisabled" class="addmodel" :class="{ gray100: filedisabled }">+添加图片</el-button>
	</div>
	<el-dialog v-model="dialogVisible" title="图片预览">
		<img :src="dialogImageUrl" style="max-width: 100%; margin: auto; display: inherit" />
	</el-dialog>
</template>
<script lang="ts" setup>
import type { UploadFile, UploadFiles, UploadInstance, UploadProps, UploadRawFile, UploadUserFile } from 'element-plus';
import { ElMessage, genFileId } from 'element-plus';
import _ from 'lodash';
import tr from 'node_modules/element-plus/es/locale/lang/tr';
import { UploadResponse } from 'types/http';
import { ref, defineExpose, defineEmits, onMounted, Ref, defineProps, nextTick, watch } from 'vue';
import { useUserStore } from '/@/store/modules/user';
import { imgSizeCheck } from '/@/utils/imgcheck';
const props = defineProps({
	model5: {
		type: [] as any,
		default: null
	}
});
import { getImgUrlBase64 } from '/@/utils/request/api/yili/api';
const userStore = useUserStore();
const activityPage: Ref<number> = ref(0); //哪家的活动标记
onMounted(() => {
	// if (userStore.pathName?.includes('shuijingfang')) {
	activityPage.value = userStore.shopType;
	// }
	if (props.model5.data[0] && props.model5.data[0].imgUrl != '') {
		var all = {
			name: props.model5.data[0].name,
			url: props.model5.data[0].imgUrl
		};
		fileList.value.push(all);
		filedisabled.value = false;
		saveimg.value.push(props.model5.data[0]);
	}
	props.model5.data.forEach((item: any) => {
		const modeljfList: any = document.getElementsByClassName('modeljfclass');
		if (modeljfnum.value == item.orderBy && item.imgUrl != '') {
			uploadFileList.value[modeljfnum.value - 1].pics[0].name = item.name;
			uploadFileList.value[modeljfnum.value - 1].pics[0].url = item.imgUrl;
			modeljfList[modeljfnum.value - 1].children[0].getElementsByClassName('input1')[0].value = item.pointCondition;
			modeljfList[modeljfnum.value - 1].children[0].getElementsByClassName('input2')[0].value = item.jumpUrl;
			saveimg.value.push(props.model5.data[modeljfnum.value]);
			if (modeljfnum.value < props.model5.data.length) {
				modeljfnum.value++;
			}
		}
	});
	console.log(uploadFileList.value, '2');
});

const init = async () => {
	props.model5.data.forEach((item: any, index: number) => {
		if (uploadFileList.value.length < props.model5.data.length - 1) {
			uploadFileList.value.push({ pics: [{ name: '', url: '' }] });
		}
	});
	console.log(props.model5.data, uploadFileList.value, '1111111');
};

const uploadFileList = ref([{ pics: [{ name: '', url: '' }] }]);
const modeljfnum = ref(1);
const zhuYiFont = ref('策略说明：优先展示小于用户当前积分的活动(积分从大到小排列)');
const emit = defineEmits(['getSaveImg', 'removeSaveImg', 'getSaveToUrl']);

const dialogVisible = ref(false);
const dialogImageUrl = ref('');
// 预览图片的时候触发
const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
	console.log('handlePictureCardPreview', uploadFile);
	dialogImageUrl.value = uploadFile.url!;
	dialogVisible.value = true;
};

const pointTactics = ref('1');
// input改变时触发向上传递
const inputchange = () => {
	const modeljfList = document.getElementsByClassName('modeljfclass');
	const str1: any = modeljfList[modeljfnum.value - 1].children[0].getElementsByClassName('input1')[0];
	const str2: any = modeljfList[modeljfnum.value - 1].children[0].getElementsByClassName('input2')[0];
	const strarr = ref([] as any);
	if (str1.value.match(/[^\d]/g)) {
		ElMessage.error('积分兑换处为负数时，不做保存处理');
		return;
	}
	strarr.value.push(str1.value);
	strarr.value.push(str2.value);
	strarr.value.push(pointTactics.value);
	emit('getSaveToUrl', strarr.value, '积分享兑  惊喜连击', modeljfnum.value);
};
watch(
	() => modeljfnum.value,
	(newValue) => {
		nextTick(() => {
			props.model5.data.forEach((item: any) => {
				const modeljfList: any = document.getElementsByClassName('modeljfclass');
				if (modeljfnum.value == item.orderBy) {
					if (uploadFileList.value.length < props.model5.data.length) {
						uploadFileList.value[modeljfnum.value - 1].pics[0].name = item.name;
						uploadFileList.value[modeljfnum.value - 1].pics[0].url = item.imgUrl;
						modeljfList[modeljfnum.value - 1].children[0].getElementsByClassName('input1')[0].value = item.pointCondition;
						modeljfList[modeljfnum.value - 1].children[0].getElementsByClassName('input2')[0].value = item.jumpUrl;
						saveimg.value.push(props.model5.data[modeljfnum.value]);
					}
				}
			});
		});
	}
);
// 下方添加按钮
const addmodel = () => {
	debugger;
	if (saveimg.value.length >= 50) {
		ElMessage.error('添加图片不可超过50张');
		return false;
	}
	const modeljfList = document.getElementsByClassName('modeljfclass');
	const str1: any = modeljfList[modeljfnum.value - 1].children[0].getElementsByClassName('input1')[0];
	const str2: any = modeljfList[modeljfnum.value - 1].children[0].getElementsByClassName('input2')[0];
	if (saveimg.value[modeljfnum.value] == '' || str1.value == '' || str2.value == '') {
		ElMessage.error('内容不能为空');
	} else {
		if (uploadFileList.value.length < props.model5.data.length - 1) {
			uploadFileList.value.push({ pics: [{ name: '', url: '' }] });
		} else {
			uploadFileList.value.push({ pics: [] });
		}
		modeljfnum.value++;
	}
};

// 上传头部信息
const fileList = ref<UploadUserFile[]>([]);
const fileList1 = ref<UploadUserFile[]>([]);

const uploader = ref<UploadInstance>();
const accept = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];

const isLoading = ref(false);
const saveimg = ref([] as any);
const handleAvatarRankSuccess = async (file: any) => {
	const imgflag = ref(false);
	let formData = new FormData();
	formData.append('file', file.file);
	// isLoading.value = true;
	const objimg = reactive({
		uid: file.file.uid,
		name: file.file.name,
		imgUrl: ''
	});
	const res = await getImgUrlBase64(formData);
	objimg.imgUrl = res;
	saveimg.value.forEach((item: any, index: number) => {
		if (item == '') {
			saveimg.value[index] = objimg;
			imgflag.value = true;
		}
	});
	if (!imgflag.value) {
		saveimg.value.push(objimg);
	}
	emit('getSaveImg', saveimg.value, '积分享兑  惊喜连击');
};

// 移除图片的时候触发
const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
	console.log('handleRemove uploadFile', uploadFile);
	console.log('handleRemove uploadFiles ', uploadFiles);
	saveimg.value.forEach((item: any, index: number) => {
		if (item.imgUrl == uploadFile.url) {
			const modeljfList = document.getElementsByClassName('modeljfclass');
			const str1: any = modeljfList[index - 1].children[0].getElementsByClassName('input1')[0];
			const str2: any = modeljfList[index - 1].children[0].getElementsByClassName('input2')[0];
			str1.value = '';
			str2.value = '';
			saveimg.value[index] = '';
			emit('removeSaveImg', index, '积分享兑  惊喜连击');
		}
	});
};
// 上传成功的时候触发
const handleUploadSuccess = (response: UploadResponse, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
	console.log('handleUploadSuccess', response, uploadFile, uploadFiles);
};

// 选择图片，移除图片的时候触发
const handleChange: UploadProps['onChange'] = (uploadFile) => {
	console.log('onChange', uploadFile);
};

let removeimg = false;
// 上传之前触发
const handleBeforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
	return new Promise((resolve: any, reject: any) => {
		if (!accept.includes(rawFile.type)) {
			ElMessage.error('不支持的图片格式');
			return false;
		}
		if (rawFile.size / 1024 / 1024 > 2) {
			ElMessage.error('图片尺寸不能超过 2MB!');
			return false;
		}
		// 校验图片尺寸
		imgSizeCheck(rawFile, 190, 240).then((res) => {
			if (!res) {
				removeimg = false;
				return reject(false);
			} else {
				removeimg = true;
				return resolve(true);
			}
		});
	});
};

const filedisabled = ref(true);
const getbtmSaveImg = (val: string) => {
	saveimg.value.push(val);
	filedisabled.value = false;
	emit('getSaveImg', saveimg.value, '积分享兑  惊喜连击');
};
const removebtmSaveImg = (uploadFile: any, uploadFiles: any) => {
	filedisabled.value = true;
	emit('removeSaveImg', 0, '积分享兑  惊喜连击');
};
const uploadimg = (val: any) => {
	fileList.value.push(val[0]);
};
init();
</script>
<style lang="scss" scoped>
.edit-page-kv::-webkit-scrollbar {
	display: none;
}
.edit-page-kv {
	width: 360px;
	height: 63vh;
	border-radius: 0px 0px 0px 0px;
	border: 1px solid #d7dde4;
	margin: 20px;
	padding: 15px;
	overflow-y: scroll;
	overflow-x: hidden;
	.btmbg {
		display: flex;
		flex-direction: row;
		align-items: center;
	}
	.page-title-content {
		height: 50px;
		background: #f0faff;
		border-radius: 5px 5px 5px 5px;
		display: flex;
		.icon-zhuyi {
			width: 16px;
			height: 14px;
			background-size: 100%;
			background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/45520/13/21134/685/630f47b3Eab613451/9da9ae33d0549548.png');
			margin-top: 8px;
			margin-left: 10px;
		}
		.p-zhuyi {
			width: auto;
			height: 29px;
			font-size: 12px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #333333;
			line-height: 12px;
			-webkit-background-clip: text;
			white-space: pre-wrap;
			margin-left: 9px;
		}
	}
	.toptitle {
		display: flex;
		flex-direction: row;
		align-items: center;
		color: red;
	}
	.toptitle1 {
		width: auto;
		height: 9px;
		font-size: 12px;
		font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 6px;
		-webkit-background-clip: text;
	}
	.btmtext {
		width: 329px;
		height: 30px;
	}
}
.image-uploader {
	margin: 0px 20px 20px 20px;
	.btmp {
		width: auto;
		height: 12px;
		font-size: 12px;
		font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: #3399ff;
		line-height: 12px;
		-webkit-background-clip: text;
	}
	&.isFull {
		:deep(.el-upload-list--picture-card .el-upload--picture-card) {
			display: none;
		}
	}

	:deep(.el-icon--close-tip) {
		display: none !important;
	}

	:deep(.el-upload-list--picture-card .el-upload-list__item) {
		width: 100px;
		height: 100px;
	}

	:deep(.el-upload--picture-card) {
		width: 100px;
		height: 100px;
	}
}
.addmodel {
	width: auto;
	height: 12px;
	font-size: 12px;
	font-family: PingFang SC-Regular, PingFang SC;
	font-weight: 400;
	color: #3399ff;
	line-height: 12px;
	-webkit-background-clip: text;
	width: 330px;
	height: 32px;
	border-radius: 2px 2px 2px 2px;
	border: 1px dashed #d7dde4;
	display: flex;
	align-items: center;
	justify-content: center;
}
.addmodel1 {
	width: auto;
	height: 12px;
	font-size: 12px;
	font-family: PingFang SC-Regular, PingFang SC;
	font-weight: 400;
	color: #ff3333;
	line-height: 12px;
	-webkit-background-clip: text;
	width: 330px;
	height: 32px;
	border-radius: 2px 2px 2px 2px;
	border: 1px dashed #d7dde4;
	display: flex;
	align-items: center;
	justify-content: center;
}
.edit-page-jf {
	width: 330px;
	height: 258px;
	background: #f9f9f9;
	margin-bottom: 20px;
	margin-top: 20px;
	.topbg {
		display: flex;
		flex-direction: row;
		.rightbg {
			display: flex;
			flex-direction: column;
			/*去掉input=number两个箭头*/
			:deep(input::-webkit-outer-spin-button),
			:deep(input::-webkit-inner-spin-button),
			:deep(input::-webkit-inner-spin-button) {
				-webkit-appearance: none;
			}

			:deep(input[type='number']) {
				-moz-appearance: textfield;
			}
			.rightbg-div1 {
				display: flex;
				flex-direction: row;
				align-items: center;
				margin-top: 20px;
			}
			.rightbg-div2 {
				display: flex;
				flex-direction: column;
				justify-content: center;
			}
			p {
				width: auto;
				height: auto;
				font-size: 12px;
				font-family: PingFang SC-常规体, PingFang SC;
				font-weight: normal;
				line-height: 12px;
				-webkit-background-clip: text;
			}
			.input1 {
				width: 96px;
				height: 30px;
				border: 1px solid #d7dde4;
			}
			.input2 {
				width: 191px;
				height: 30px;
				border: 1px solid #d7dde4;
			}
		}
	}
	.image-uploader1 {
		margin: 20px 15px;
		height: 108px;
		overflow: hidden;
		.btmp {
			width: auto;
			height: 12px;
			font-size: 12px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #3399ff;
			line-height: 12px;
			-webkit-background-clip: text;
		}
		&.isFull {
			:deep(.el-upload-list--picture-card .el-upload--picture-card) {
				display: none;
			}
		}

		:deep(.el-icon--close-tip) {
			display: none !important;
		}

		:deep(.el-upload-list--picture-card .el-upload-list__item) {
			width: 100px;
			height: 100px;
		}

		:deep(.el-upload--picture-card) {
			width: 100px;
			height: 100px;
		}
	}
	.image-uploader {
		margin: 15px 0;
		.btmp {
			width: auto;
			height: 12px;
			font-size: 12px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #3399ff;
			line-height: 12px;
			-webkit-background-clip: text;
		}
		&.isFull {
			:deep(.el-upload-list--picture-card .el-upload--picture-card) {
				display: none;
			}
		}

		:deep(.el-icon--close-tip) {
			display: none !important;
		}

		:deep(.el-upload-list--picture-card .el-upload-list__item) {
			width: 330px;
			height: 32px;
		}

		:deep(.el-upload--picture-card) {
			width: 330px;
			height: 32px;
		}
	}
}
.gray100 {
	filter: grayscale(100%);
}
</style>
