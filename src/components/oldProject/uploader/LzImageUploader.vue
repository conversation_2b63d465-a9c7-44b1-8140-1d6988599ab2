<template>
	<el-upload
		ref="uploader"
		class="image-uploader"
		list-type="picture-card"
		v-model:file-list="fileList"
		:disabled="imgloaddisabled"
		:class="{ isFull: fileList.length >= props.limit }"
		:accept="accept.join(',')"
		:multiple="limit > 1"
		:limit="limit"
		action=""
		:headers="headers"
		:before-upload="handleBeforeUpload"
		:on-change="handleChange"
		:http-request="handleAvatarRankSuccess"
		:on-preview="handlePictureCardPreview"
		:on-success="handleUploadSuccess"
		:on-remove="handleRemove"
		v-loading="isLoading"
		:on-exceed="handleExceed"
	>
		<el-space direction="vertical" :class="{ gray100: imgloaddisabled }">
			<span class="centerimg"></span>
			<p class="centerp">请上传图片</p>
		</el-space>
	</el-upload>
	<el-dialog v-model="dialogVisible" title="图片预览">
		<img :src="dialogImageUrl" style="max-width: 100%; margin: auto; display: inherit" />
	</el-dialog>
</template>

<script lang="ts" setup>
import type { UploadFile, UploadFiles, UploadInstance, UploadProps, UploadRawFile, UploadUserFile } from 'element-plus';
import { ElMessage, genFileId } from 'element-plus';
import _ from 'lodash';
import { UploadResponse } from 'types/http';
// import { baseURL } from '~/utils/http';
import { useUserStore } from '/@/store/modules/user.ts';
import { defineProps, Ref, getCurrentInstance, defineEmits, ref } from 'vue';
import { getImgUrlBase64 } from '/@/utils/request/api/yili/api.ts';

const userStore = useUserStore();

// 定义事件
const emit = defineEmits(['uploadimg', 'getbtmSaveImg', 'removebtmSaveImg']);

// 接受参数
const props = defineProps({
	fileList: {
		type: (<UploadUserFile[]>[]) as any,
		default: null
	},
	imgloaddisabled: {
		type: Boolean,
		default: false
	},
	width: {
		type: Number,
		default: 0
	},
	height: {
		type: Number,
		default: 0
	},
	aspectRatio: {
		type: Number,
		default: 2
	},
	limit: {
		type: Number,
		default: 1
	}
});
const fileList: Ref<UploadUserFile[]> = props.fileList;

// 上传头部信息
const headers = {
	AccessToken: userStore.token
};
// const uploadAction = `${baseURL}/fileUpload/getImgUrlBase64`;
const dialogImageUrl = ref('');
const dialogVisible = ref(false);

const uploader = ref<UploadInstance>();
const accept = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];

// 向上传递值
const doEmit = (uploadFiles: UploadFiles) => {
	emit('uploadimg', uploadFiles);
};

// 移除图片的时候触发
const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
	console.log('handleRemove uploadFile', uploadFile);
	console.log('handleRemove uploadFiles ', uploadFiles);
	emit('removebtmSaveImg', uploadFile, uploadFiles);
};

// 预览图片的时候触发
const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
	console.log('handlePictureCardPreview', uploadFile);
	dialogImageUrl.value = uploadFile.url!;
	dialogVisible.value = true;
};

// import http from '~/utils/http';
const isLoading = ref(false); // 上传图片Loading
const handleAvatarRankSuccess = async (file: any) => {
	let formData = new FormData();
	formData.append('file', file.file);
	// isLoading.value = true;
	const res = await getImgUrlBase64(formData);
	isLoading.value = false;
	emit('getbtmSaveImg', res);
};
// 上传成功的时候触发
const handleUploadSuccess = (response: UploadResponse, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
	console.log('handleUploadSuccess', response, uploadFile, uploadFiles);
	doEmit(uploadFiles);
};

// 选择图片，移除图片的时候触发
const handleChange: UploadProps['onChange'] = (uploadFile) => {
	console.log('onChange', uploadFile);
};

import { imgSizeCheck } from '/@/utils/imgcheck';
let removeimg = false;
// 上传之前触发
const handleBeforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
	return new Promise((resolve: any, reject: any) => {
		if (!accept.includes(rawFile.type)) {
			ElMessage.error('不支持的图片格式');
			return reject(false);
		}
		if (rawFile.size / 1024 / 1024 > 2) {
			ElMessage.error('图片大小不能超过 2MB!');
			return reject(false);
		}
		// 校验图片尺寸
		imgSizeCheck(rawFile, props.width, props.height).then((res) => {
			if (!res) {
				removeimg = false;
				return reject(false);
			} else {
				removeimg = true;
				return resolve(true);
			}
		});
	});
};

// 当选择的文件超过限制
const handleExceed: UploadProps['onExceed'] = (files) => {
	const sLen = props.limit - fileList.value.length;
	for (let i = 0; i < sLen; i++) {
		const file = files[i] as UploadRawFile;
		file.uid = genFileId();
		uploader.value!.handleStart(file);
	}
};
</script>

<style lang="scss" scoped>
@use 'sass:math';

@mixin uploadSize {
	width: v-bind('props.width');
	height: v-bind('props.height');
	aspect-ratio: v-bind('props.aspectRatio');
}

.image-uploader {
	.centerimg {
		width: 40px;
		height: 36px;
		background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/156195/4/29925/876/6315ae46E1bf68910/6414178d96316c3a.png');
		background-size: 100%;
	}
	.centerp {
		width: 60px;
		height: 12px;
		font-size: 12px;
		font-family: PingFang SC-常规体, PingFang SC;
		font-weight: normal;
		color: #9ca7b5;
		line-height: 12px;
		-webkit-background-clip: text;
	}
	&.isFull {
		:deep(.el-upload-list--picture-card .el-upload--picture-card) {
			display: none;
		}
	}

	:deep(.el-icon--close-tip) {
		display: none !important;
	}

	:deep(.el-upload-list--picture-card .el-upload-list__item) {
		// @include uploadSize;
		width: 330px;
		height: 177px;
	}

	:deep(.el-upload--picture-card) {
		// @include uploadSize;
		width: 330px;
		height: 177px;
	}
}
.gray100 {
	filter: grayscale(100%);
}
</style>
