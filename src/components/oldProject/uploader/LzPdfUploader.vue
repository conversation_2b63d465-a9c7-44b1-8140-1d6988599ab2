<template>
  <el-upload :before-upload="handleBeforeUpload" :headers="headers" :http-request="httpRequest"
    :on-change="handleChange" :show-file-list="false" accept=".pdf" class="pdf-uploader" drag>
    <template v-if="uploadStatus === 0">
      <el-icon class="el-icon--upload">
        <UploadFilled />
      </el-icon>
      <div class="el-upload__text">
        拖拽文件或者 <em>点击选择文件</em>
      </div>
    </template>
    <el-progress v-if="uploadStatus === 1" :percentage="uploadPercent" class="progress" type="circle">
      <template #default="{ percentage }">
        <span class="percentage-value">{{ percentage }}%</span>
      </template>
    </el-progress>
    <img v-if="uploadStatus === 2" alt="" class="el-upload-list__item-thumbnail" src="/icons/pdf.png" />
  </el-upload>
</template>

<script lang="ts" setup>
import type { UploadProps, UploadRequestOptions } from 'element-plus';
import { UploadFile } from 'element-plus';
import * as PdfJs from 'pdfjs-dist/legacy/build/pdf.js'; // 注意导入的写法
import * as workerSrc from 'pdfjs-dist/build/pdf.worker.entry.js';
import { trim, truncate } from 'lodash';
import { PDFDocumentProxy, PDFPageProxy, TextContent, TextItem, TextMarkedContent } from 'pdfjs-dist/types/src/display/api';
import type { MultipartUpload, UploadResponse } from "types/http";
import useUserStore from "~/store/user";
import { completeMultipartUpload, multipartUpload, startMultipartUpload } from "~/apis/upload";

const userStore = useUserStore();

// 接受参数
const props = defineProps({
  modelValue: {
    type: String,
    required: true,
    default: '',
  },
  text: {
    type: String,
    default: '',
  },
  pages: {
    type: Number,
    default: 0,
  },
});

const uploadPercent = ref(0);
const uploadStatus = ref(props.modelValue ? 2 : 0);

const headers = {
  AccessToken: userStore.accessToken,
}

// 设定pdfjs的 workerSrc 参数
PdfJs.GlobalWorkerOptions.workerSrc = workerSrc;

// 定义事件
const emit = defineEmits(['update:modelValue', 'change']);

const fileUrl = computed(() => props.modelValue);

const handleBeforeUpload: UploadProps['beforeUpload'] = (rawFile) =>
  // if (![".pdf"].includes(rawFile.type)) {
  //   ElMessage.error("不支持的图片格式");
  //   return false;
  // } else if (rawFile.size / 1024 / 1024 > 2) {
  //   ElMessage.error("图片尺寸不能超过 2MB!");
  //   return false;
  // }
  true;

const getPageText = async (pageNum: number, pdf: PDFDocumentProxy) => {
  const pdfPage: PDFPageProxy = await pdf.getPage(pageNum);
  const textContent: TextContent = await pdfPage.getTextContent();
  return textContent.items
    .filter((item) => item as TextItem)
    .map((item: TextItem | TextMarkedContent) => {
      item = item as TextItem;
      return trim(item.str);
    })
    .join('');
};

const getSummaryText = async (file: File): Promise<void> => {
  const url = URL.createObjectURL(file!);
  const loadingTask = PdfJs.getDocument(url);
  const pdf = await loadingTask.promise;
  const { numPages } = pdf;
  let text = '';
  for (let i = 1; i <= numPages && text.length < 250; i++) {
    text += await getPageText(i, pdf);
  }
  text = truncate(text, {
    length: 250,
  });
  emit('change', file.name, text, numPages);
};

const onSuccess = (response: UploadResponse) => {
  console.log(' onSuccess', response)
  const { errno, data } = response;
  if (errno === 0 && data) {
    const { url } = data;
    emit('update:modelValue', url);
  }
  uploadStatus.value = 2;
};

const onProgress = (percent: number) => {
  console.log(' onProgress', percent)
  uploadPercent.value = percent;
};

const onError = (error: Error) => {
  console.log('onError', error)
  uploadStatus.value = 0;
};

const handleChange: UploadProps['onChange'] = (uploadFile: UploadFile) => {
  getSummaryText(uploadFile.raw!);
};

const sliceFile = (file: File) => {
  const size = file.size; //总大小shardSize = 2 * 1024 * 1024,
  const shardSize = 1024 * 1024 * 2;//以2MB为一个分片
  const shardCount = Math.ceil(size / shardSize); //总片数
  let i = 0;
  const blobList = [];

  while (i < shardCount) {
    //计算每一片的起始与结束位置
    const start = i * shardSize;
    const end = Math.min(size, start + shardSize);
    //slice方法用于切出文件的一部分
    const sliceData = file.slice(start, end);
    blobList.push(sliceData);
    i++;
  }

  return {
    fragment: blobList,
    total: shardCount
  };
}

const httpRequest = async (options: UploadRequestOptions): Promise<unknown> => {
  uploadStatus.value = 1;

  const { fragment, total } = sliceFile(options.file);
  let progress = 0;

  const params: MultipartUpload = {
    filename: options.file.name,
    contentLength: options.file.size,
    contentType: options.file.type,
  };

  console.log('params', params)

  // 获取上传分组号
  let uploadId = await startMultipartUpload(params);

  console.log('uploadId', uploadId)

  const formDataList = fragment.map((part, index) => {
    const form = new FormData();
    form.append("part", part); //slice方法用于切出文件的一部分
    form.append("total", total.toString()); //总片数
    form.append("partSize", part.size.toString()); //当前是第几片
    form.append("partNumber", (index + 1).toString()); //当前是第几片
    form.append("uploadId", uploadId); //当前是第几片
    return form;
  })

  const postQueue = formDataList.map(async (formData) => {
    await multipartUpload(formData);
    progress++;
    onProgress(Math.round(progress * 100 / total));
  });

  return Promise.all(postQueue)
    .then(async (values) => {
      const uploadResult = await completeMultipartUpload(uploadId);
      onSuccess(uploadResult);
    })
    .catch(e => {
      console.error("all catch", e)
      onError(e);
    });
}
</script>

<style lang="scss" scoped>
@use 'sass:math';

$width: 214px;
$height: 137px;

.pdf-uploader {

  :deep(.el-upload-dragger) {
    width: $width;
    height: $height;
    padding: var(--el-upload-dragger-padding-horizontal) var(--el-upload-dragger-padding-vertical);
    background-color: var(--el-fill-color-blank);
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    box-sizing: border-box;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    --el-upload-dragger-padding-horizontal: 10px;
    --el-upload-dragger-padding-vertical: 20px;
  }

  .progress {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 1;

    width: 100px !important;
    height: 100px !important;
    transform: translate(-50%, -50%);

    .percentage-value {
      display: block;
      font-size: 18px;
    }

    :deep(.el-progress-circle) {
      width: 100% !important;
      height: 100% !important;
    }
  }

  .el-upload-list__item-thumbnail {
    height: 100%;
    margin: 0 auto;
    padding: 10px;
  }
}
</style>
