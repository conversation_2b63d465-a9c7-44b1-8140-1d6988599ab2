<template>
	<a-button
		:disabled="disabled"
		:type="type"
		@click="handleButtonClick"
		:shape="shape"
		:danger="danger"
		v-if="useUserStoreObj.permissionKeyList.indexOf(PERM_KEY[permissionKey]) != -1 || !permissionKey"
		>{{ btnTxt }}</a-button
	>
	<!-- <a-button :type="type" @click="handleButtonClick" :shape="shape" :danger="danger" >{{ btnTxt }}</a-button> -->
</template>

<script setup lang="ts">
import { useUserStore } from '/@/store/modules/user';
import { PERM_KEY } from '../utils/permEnum';
// const props = defineProps(['type', 'btnTxt', 'shape', 'danger', 'permissionKey']);
const props = defineProps({
	permissionKey: {
		type: String,
		default: ''
	},
	type: {
		type: String,
		default: 'primary'
	},
	btnTxt: {
		type: String,
		default: ''
	},
	shape: {
		type: Boolean,
		default: false
	},
	danger: {
		type: Boolean,
		default: false
	},
	disabled: {
		type: Boolean,
		default: false
	}
});
const useUserStoreObj = useUserStore();
const emit = defineEmits(['btnClick']);

const handleButtonClick = () => {
	// const emit = defineEmits(['refreshPermData', 'refreshRoleData', 'refreshUserData']);
	emit('btnClick');
};
</script>

<style lang="less" scoped></style>
