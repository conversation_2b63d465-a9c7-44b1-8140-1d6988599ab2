
import { RouteRecordRaw } from 'vue-router';

// 获取动态路由模块
export const getRouteModuleList = async () => {
  const routeModuleList: RouteRecordRaw[] = [];

  // 动态引入modules目录下的所有路由模块
  const modules = import.meta.glob('./modules/**/*.ts');

  // 加入到动态路由集合中
  for (const path in modules) {
    const modObj = await modules[path]();
    // @ts-ignore
    const mod = modObj.default || {};
    const modList = Array.isArray(mod) ? [...mod] : [mod];
    routeModuleList.push(...modList);
  }

  return routeModuleList;
};

// 静态路由列表
export const staticRoutesList: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Root',
    redirect: '/login',
    meta: {
      title: 'Root',
      layout: true,
      menu: false,
      icon: 'DashboardOutLined',
      sort: 1,
    },
  }, {
    path: '/login',
    name: 'Login',
    component: () => import('/@/views/base/login.vue'),
    meta: {
      title: '抖音定制后台管理',
      layout: false,
      menu: false,
      icon: 'LoginOutLined',
    }
  }, {
    path: '/modify',
    name: 'Modify',
    component: () => import('/@/views/base/modify.vue'),
    meta: {
      title: '修改密码',
      layout: false,
      menu: false,
      icon: 'LoginOutLined',
    }
  }, {
    path: '/loading',
    name: 'Loading',
    component: () => import('/@/views/base/loading.vue'),
    meta: {
      title: '加载页面',
      layout: false,
      menu: false,
      icon: 'LoginOutLined',
    }
  }
];