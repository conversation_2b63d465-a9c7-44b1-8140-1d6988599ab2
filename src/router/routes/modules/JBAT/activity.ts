import { routeConfig } from '../../routeConfig';
import { PERM_KEY } from '/@/utils/permEnum';

export const JBATRoutes: routeConfig[] = [
	{
		path: '/jbat',
		name: 'jbat',
		component: 'views/JBAT/index.vue',
		meta: {
			title: '澳优',
			layout: true,
			menu: true,
			icon: 'GiftOutlined',
			sort: 1,
			permissionKey: PERM_KEY.JBAT_ACTIVE
		},
		children: [
			{
				path: '/jbat/list',
				name: 'jbatList',
				component: 'views/JBAT/pages/activeList.vue',
				meta: {
					title: '新客礼',
					layout: true,
					menu: true,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_ACTIVE_LIST
				}
			},
			{
				path: '/jbat/editActive',
				name: 'jbatEditActive',
				component: 'views/JBAT/pages/editActive.vue',
				meta: {
					title: '新客礼',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_EDIT_ACTIVE
				}
			},
			{
				path: '/jbat/record',
				name: 'jbatRecord',
				component: 'views/JBAT/pages/record.vue',
				meta: {
					title: '新客礼活动记录',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_RECOED
				}
			},
			{
				path: '/jbat/Report',
				name: 'jbatReport',
				component: 'views/JBAT/pages/report.vue',
				meta: {
					title: '新客礼数据报表',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_REPORT
				}
			},
			{
				path: '/jbat/listNewGuestGifts1_0',
				name: 'jbatListNewGuestGifts1_0',
				component: 'views/JBAT/NewGuestGifts1_0/pages/activeList.vue',
				meta: {
					title: '新客礼1.0',
					layout: true,
					menu: true,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_ACTIVE_LIST_1_0
				}
			},
			{
				path: '/jbat/editActive1_0',
				name: 'jbatEditActive1_0',
				component: 'views/JBAT/NewGuestGifts1_0/pages/editActive.vue',
				meta: {
					title: '新客礼1.0',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_EDIT_ACTIVE_1_0
				}
			},
			{
				path: '/jbat/record1_0',
				name: 'jbatRecord1_0',
				component: 'views/JBAT/NewGuestGifts1_0/pages/record.vue',
				meta: {
					title: '新客礼1.0活动记录',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_RECOED_1_0
				}
			},
			{
				path: '/jbat/Report1_0',
				name: 'jbatReport1_0',
				component: 'views/JBAT/NewGuestGifts1_0/pages/report.vue',
				meta: {
					title: '新客礼1.0数据报表',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_REPORT_1_0
				}
			},
			{
				path: '/jbat/listNewGuestGifts2_0',
				name: 'jbatListNewGuestGifts2_0',
				component: 'views/JBAT/NewGuestGifts2_0/pages/activeList.vue',
				meta: {
					title: '新客礼2.0',
					layout: true,
					menu: true,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_ACTIVE_LIST_2_0
				}
			},
			{
				path: '/jbat/editActive2_0',
				name: 'jbatEditActive2_0',
				component: 'views/JBAT/NewGuestGifts2_0/pages/editActive.vue',
				meta: {
					title: '新客礼2.0',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_EDIT_ACTIVE_2_0
				}
			},
			{
				path: '/jbat/record2_0',
				name: 'jbatRecord2_0',
				component: 'views/JBAT/NewGuestGifts2_0/pages/record.vue',
				meta: {
					title: '新客礼2.0活动记录',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_RECOED_2_0
				}
			},
			{
				path: '/jbat/Report2_0',
				name: 'jbatReport2_0',
				component: 'views/JBAT/NewGuestGifts2_0/pages/report.vue',
				meta: {
					title: '新客礼2.0数据报表',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_REPORT_2_0
				}
			},
			{
				path: '/jbat/listNewGuestGifts',
				name: 'jbatListNewGuestGifts',
				component: 'views/JBAT/CanCollectionGift/pages/activeList.vue',
				meta: {
					title: '集罐有礼',
					layout: true,
					menu: true,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_CAN_COLLECTION_GIFT
				}
			},
			{
				path: '/jbat/CanCollectionGiftEdit',
				name: 'jbatCanCollectionGiftEdit',
				component: 'views/JBAT/CanCollectionGift/pages/editActive.vue',
				meta: {
					title: '集罐有礼',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_EDIT_CAN_COLLECTION_GIFT
				}
			},
			{
				path: '/jbat/CanCollectionGiftRecord',
				name: 'jbatCanCollectionGiftRecord',
				component: 'views/JBAT/CanCollectionGift/pages/record.vue',
				meta: {
					title: '集罐有礼活动记录',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_CAN_COLLECTION_GIFT_LIST
				}
			},
			{
				path: '/jbat/CanCollectionGiftReport',
				name: 'jbatCanCollectionGiftReport',
				component: 'views/JBAT/CanCollectionGift/pages/report.vue',
				meta: {
					title: '集罐有礼数据报表',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_CAN_COLLECTION_GIFT_REPORT
				}
			},
			{
				path: '/jbat/CanCollectionReportShop',
				name: 'CanCollectionReportShop',
				component: 'views/JBAT/CanCollectionGift/pages/shopsReport.vue',
				meta: {
					title: '集罐有礼店铺报表',
					layout: true,
					menu: true,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_CAN_COLLECTION_REPORT_SHOP
				}
			},
			{
				path: '/jbat/RepurchaseGift',
				name: 'jbatRepurchaseGift',
				component: 'views/JBAT/RepurchaseGift/pages/activeList.vue',
				meta: {
					title: '复购礼',
					layout: true,
					menu: true,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_REPURCHASE_GIFT
				}
			},
			{
				path: '/jbat/EditRepurchaseGift',
				name: 'jbatEditRepurchaseGift',
				component: 'views/JBAT/RepurchaseGift/pages/editActive.vue',
				meta: {
					title: '复购礼',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_EDIT_REPURCHASE_GIFT
				}
			},
			{
				path: '/jbat/RepurchaseGiftRecord',
				name: 'jbatRepurchaseGiftRecord',
				component: 'views/JBAT/RepurchaseGift/pages/record.vue',
				meta: {
					title: '复购礼活动记录',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_REPURCHASE_GIFT_LIST
				}
			},
			{
				path: '/jbat/RepurchaseGiftReport',
				name: 'jbatRepurchaseGiftReport',
				component: 'views/JBAT/RepurchaseGift/pages/report.vue',
				meta: {
					title: '复购礼数据报表',
					layout: true,
					menu: false,
					icon: 'SettingOutlined',
					sort: 1,
					permissionKey: PERM_KEY.JBAT_REPURCHASE_GIFT_REPORT
				}
			},
		]
	}
];
