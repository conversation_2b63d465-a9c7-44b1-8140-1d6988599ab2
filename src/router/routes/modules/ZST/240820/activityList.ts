import { routeConfig } from "../../../routeConfig";
import { PERM_KEY } from "/@/utils/permEnum";

export const interactiveGiftDataRoutes: routeConfig[] = [
    {
        path: '/interactiveGift',
        name: 'interactiveGift',
        component: 'views/ZST/240820/index.vue',
        meta: {
            title: '进店领积分',
            layout: true,
            menu: true,
            icon: 'GiftOutlined',
            sort: 1,
            permissionKey: PERM_KEY.ZST_SEND_SCORE
        },
        children: [
            {
                path: '/interactiveGift/list',
                name: 'interactiveGiftList',
                component: 'views/ZST/240820/activityList/index.vue',
                meta: {
                    title: '活动管理',
                    layout: true,
                    menu: true,
                    icon: 'SettingOutlined',
                    sort: 1,
                    permissionKey: PERM_KEY.ZST_SEND_SCORE
                },
            },
            {
                path: '/interactiveGift/dataRecord/:data?',
                name: 'dataRecord',
                component: 'views/ZST/240820/dataRecord/index.vue',
                meta: {
                    title: '用户详情',
                    layout: true,
                    menu: false,
                    icon: 'BarsOutlined',
                    sort: 2,
                    // permissionKey: PERM_KEY.ZST_SEND_SCORE
                },
            },
            {
                path: '/interactiveGift/dataAnalysis/:data?',
                name: 'dataAnalysis',
                component: 'views/ZST/240820/dataAnalysis/index.vue',
                meta: {
                    title: '活动数据',
                    layout: true,
                    menu: false,
                    icon: 'BarsOutlined',
                    sort: 3,
                    // permissionKey: PERM_KEY.ZST_SEND_SCORE
                },
            },
            {
                path: '/interactiveGift/acyivitySetting/:data?',
                name: 'acyivitySetting',
                component: 'views/ZST/240820/acyivitySetting/index.vue',
                meta: {
                    title: '活动配置',
                    layout: true,
                    menu: false,
                    icon: 'BarsOutlined',
                    sort: 4,
                    // permissionKey: PERM_KEY.ZST_SEND_SCORE
                },
            },
        ]
    }
]
