import { routeConfig } from "../../../routeConfig";
import { PERM_KEY } from "/@/utils/permEnum";

export const birthdayGiftDataRoutes: routeConfig[] = [
    {
        path: '/birthdayGiftData',
        name: 'birthdayGiftData',
        component: 'views/ZST/240515/birthdayGift/index.vue',
        meta: {
            title: '生日礼',
            layout: true,
            menu: true,
            icon: 'GiftOutlined',
            sort: 1,
            permissionKey: PERM_KEY.ZST_BIRTHDAYGIFT
        },
        children: [
            {
                path: '/birthdayGiftData/main',
                name: 'birthdayGiftDataMain',
                component: 'views/ZST/240515/birthdayGift/main.vue',
                meta: {
                    title: '活动数据',
                    layout: true,
                    menu: true,
                    icon: 'AreaChartOutlined',
                    sort: 1,
                    permissionKey: PERM_KEY.ZST_BIRTHDAYGIFT_DATA
                },
            },
            {
                path: '/birthdayGiftData/userInfo',
                name: 'birthdayGiftUserInfo',
                component: 'views/ZST/240515/birthdayGift/info.vue',
                meta: {
                    title: '用户详情',
                    layout: true,
                    menu: true,
                    icon: 'IdcardOutlined',
                    sort: 1,
                    permissionKey: PERM_KEY.ZST_BIRTHDAYGIFT_USER
                },
            },
        ]
    }
]
