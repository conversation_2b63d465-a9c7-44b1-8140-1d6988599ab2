import { routeConfig } from "../routeConfig";
import { PERM_KEY } from "/@/utils/permEnum";

export const authManageRoutes: routeConfig[] = [
    {
        path: '/authManage',
        name: 'AuthManage',
        component: 'views/authManage/index.vue',
        meta: {
            title: '权限管理',
            layout: true,
            menu: true,
            icon: 'SettingOutlined',
            sort: 1,
            permissionKey: PERM_KEY.SYS
        },
        children: [
            {
                path: '/authManage/userManage',
                name: 'UserManage',
                component: 'views/authManage/userManage/index.vue',
                meta: {
                    title: '用户管理',
                    layout: true,
                    menu: true,
                    icon: 'UserOutlined',
                    sort: 1,
                    permissionKey: PERM_KEY.SYS_USER
                },
            }, {
                path: '/authManage/roleManage',
                name: 'RoleManage',
                component: 'views/authManage/roleManage/index.vue',
                meta: {
                    title: '角色管理',
                    layout: true,
                    menu: true,
                    icon: 'TeamOutlined',
                    sort: 2,
                    permissionKey: PERM_KEY.SYS_ROLE
                },
            }, {
                path: '/authManage/permManage',
                name: 'PermManage',
                component: 'views/authManage/permManage/index.vue',
                meta: {
                    title: '权限管理',
                    layout: true,
                    menu: true,
                    icon: 'ControlOutlined',
                    sort: 3,
                    permissionKey: PERM_KEY.SYS_PERMS
                },
            }
        ]
    }
]