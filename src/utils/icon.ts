import { createVNode } from "vue"
import { DashOutlined, InboxOutlined, GiftOutlined,BgColorsOutlined,FireOutlined, AreaChartOutlined, IdcardOutlined, MenuOutlined, KeyOutlined, BarsOutlined, SettingOutlined, UserOutlined, TeamOutlined, ControlOutlined, EditOutlined } from '@ant-design/icons-vue'
const $Icon = {
  DashOutlined, InboxOutlined, GiftOutlined,BgColorsOutlined,FireOutlined, AreaChartOutlined, IdcardOutlined, MenuOutlined, KeyOutlined, BarsOutlined, SettingOutlined, UserOutlined, TeamOutlined, ControlOutlined, EditOutlined
}

const Icon = (props: { icon: string }) => {
  const { icon } = props;
  const antIcon: { [key: string]: any } = $Icon;
  return createVNode(antIcon[icon])
}

export default Icon;