import Fetch from "../../http";

// 活动列表
export function getPageList(data) {
  return Fetch({
    url: '/ausnutria/repurchase/getPageList',
    method: 'post',
    data
  })
}

// 新增活动
export function addActivity(data) {
  return Fetch({
    url: '/ausnutria/repurchase/addActivity',
    method: 'post',
    data
  })
}

// 修改活动
export function updateActivity(data) {
  return Fetch({
    url: '/ausnutria/repurchase/updateActivity',
    method: 'post',
    data
  })
}

// 删除活动
export function deleteActivity(data) {
  return Fetch({
    url: '/ausnutria/repurchase/deleteActivity',
    method: 'post',
    data
  })
}

// 活动详情
export function getActivityDetail(data) {
  return Fetch({
    url: '/ausnutria/repurchase/getActivityDetail',
    method: 'post',
    data
  })
}

// 报名记录
export function activitySignRecordPage(data) {
  return Fetch({
    url: '/ausnutria/repurchase/activitySignRecordPage',
    method: 'post',
    data
  })
}

// 导出报名记录
export function exportActivitySignRecord(data) {
  return Fetch({
    url: '/ausnutria/repurchase/exportActivitySignRecord',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 领取记录分页查询
export function activityReceiveRecordPage(data) {
  return Fetch({
    url: '/ausnutria/repurchase/activityReceiveRecordPage',
    method: 'post',
    data
  })
}

// 导出领取记录
export function exportActivityReceiveRecord(data) {
  return Fetch({
    url: '/ausnutria/repurchase/exportActivityReceiveRecord',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 出生证绑定记录分页查询
export function bornCertificateRecordPage(data) {
  return Fetch({
    url: '/ausnutria/repurchase/bornCertificateRecordPage',
    method: 'post',
    data
  })
}

// 导出出生证绑定记录
export function exportBornCertificateRecord(data) {
  return Fetch({
    url: '/ausnutria/repurchase/exportBornCertificateRecord',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 获取活动基础数据
export function basicData(data) {
  return Fetch({
    url: '/ausnutria/repurchase/basicData',
    method: 'post',
    data
  })
}

// 导出活动基础数据
export function exportBasicData(data) {
  return Fetch({
    url: '/ausnutria/repurchase/exportBasicData',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 获取活动详情数据
export function basicDataByDay(data) {
  return Fetch({
    url: '/ausnutria/repurchase/basicDataByDay',
    method: 'post',
    data
  })
}

// 导出活动详情数据
export function exportBasicDataByDay(data) {
  return Fetch({
    url: '/ausnutria/repurchase/exportBasicDataByDay',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 下载SKU模板
export function exportSkuTemplate() {
  return Fetch({
    url: '/ausnutria/repurchase/skuTemplate/export',
    method: 'post',
    responseType: 'blob'
  })
}

// 导入SKU列表
export function importSkuExcel(data) {
  return Fetch({
    url: '/ausnutria/repurchase/importSkuExcel',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export function addScene(data) {
  return Fetch({
    url: '/ausnutria/repurchase/addScene',
    method: 'post',
    data
  })
}
