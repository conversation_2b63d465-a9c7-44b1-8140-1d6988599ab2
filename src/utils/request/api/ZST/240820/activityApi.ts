import Fetch from "../../../http";
// 查询活动详情
export function getActiveInfo(data) {
    return Fetch({
        method: 'GET',
        url: '/shiseido/membergift/activity/info',
        params: data
    })
}
// 结束活动
export function stopActive(data) {
    return Fetch({
        method: 'GET',
        url: '/shiseido/membergift/activity/stop',
        params: data
    })
}
// 删除活动
export function delActive(data) {
    return Fetch({
        method: 'GET',
        url: '/shiseido/membergift/activity/delete',
        params: data
    })
}
// 查询活动汇总数据
export function getTotalData(data) {
    return Fetch({
        method: 'GET',
        url: '/shiseido/membergift/activity/data/total',
        params: data
    })
}
// 查询活动列表数据
export function getActiveData(data) {
    return Fetch({
        method: 'GET',
        url: '/shiseido/membergift/activity/data/list',
        params: data
    })
}
// 查询用户详情
export function getRecordList(data) {
    return Fetch({
        method: 'GET',
        url: '/shiseido/membergift/activity/member/gift/record/list',
        params: data
    })
}
// 查询活动列表
export function getActivityList(data) {
    return Fetch({
        url: '/shiseido/membergift/activity/list',
        method: 'post',
        data
    })
}
// 创建/更新活动
export function saveActive(data) {
    return Fetch({
        url: '/shiseido/membergift/activity/save',
        method: 'post',
        data
    })
}
// 上传图片
export function upImage(data) {
    return Fetch({
        url: '/shiseido/membergift/activity/image',
        method: 'post',
        data
    })
}
// 导出活动列表数据
export function activeDataExport(data) {
    return Fetch({
        method: 'GET',
        url: '/shiseido/membergift/activity/data/list/export',
        params: data,
        responseType: 'blob'
    })
}
// 导出用户详情
export function recordDataExport(data) {
    return Fetch({
        method: 'GET',
        url: '/shiseido/membergift/activity/member/gift/record/export',
        params: data,
        responseType: 'blob'
    })
}