import base from './base';
import axios, { AxiosRequestConfig } from 'axios';
import { loadingStore } from "/@/store/modules/loading";
import { useUserStore } from "/@/store/modules/user";
import { Modal, message } from 'ant-design-vue';
const loadingStoreObj = loadingStore();
const useUserStoreObj = useUserStore()
import { useRouter } from 'vue-router';
import { ssoLogOut } from './api/SKII/mainApi';

let loginStatus = false

const instance = axios.create({
  timeout: 1000 * 60
})

instance.defaults.baseURL = base.baseurl;

interface AxiosConfig extends AxiosRequestConfig {
  loading?: boolean
}

// token: useUserStoreObj.token
const Fetch = ({ url = "", method = 'GET', data = {}, params = {}, headers = { 'Content-Type': 'application/json' }, responseType, loading = true }: AxiosConfig) => {
  const router = useRouter();
  if (useUserStoreObj.token) {
    Object.assign(headers, {
      'jddz-b-token': useUserStoreObj.token,
      'token': useUserStoreObj.token
    })
  }
  if (loading) {
    loadingStoreObj.showLoading();
  }
  return new Promise((resolve, reject) => {
    instance({
      url,
      method,
      data,
      params,
      headers,
      responseType
    }).then(res => {
      loadingStoreObj.hideLoading()
      console.log(res, url.indexOf('export'))
      if (loginStatus) {
        return
      }
      if (res.status === 200) {
        if (res.data.code === 200) {
          resolve(res.data.data)
        } else if (res.data.code === 401) {
          loginStatus = true
          Modal.confirm({
            title: '提示',
            content: '当前登录状态已过期，请重新登录',
            okText: '重新登录',
            cancelButtonProps: { style: { display: 'none' } },
            cancelText: '取消',
            onOk() {
              loginStatus = false
              window.location.replace('/cjtd-b/login')
              // router.push({ path: '/login', replace: true });
              useUserStoreObj.clearToken()
              useUserStoreObj.clearUserInfo()
            },
          });
        } else if (url.indexOf('export') !== -1 || url.indexOf('publishAct/publish') !== -1) {
          resolve(res)
        } else {
          message.error(res.data.message);
          reject(res.data);
        }
      }

    }).catch(err => {
      loadingStoreObj.hideLoading()
      reject(err)
    })
  })
};

export default Fetch;