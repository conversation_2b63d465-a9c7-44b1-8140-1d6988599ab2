const PERM_KEY = {
	SYS: 'sys_manager',
	SYS_USER: 'sys_user',
	SYS_USER_CREATE: 'sys_user_create',
	SYS_USER_UPD: 'sys_user_upd',
	SYS_USER_DEL: 'sys_user_del',
	SYS_USER_BIND: 'sys_user_bind',
	SYS_USER_REPWD: 'sys_user_rePwd',
	SYS_ROLE: 'sys_role',
	SYS_ROLE_CREATE: 'sys_role_create',
	SYS_ROLE_UPD: 'sys_role_upd',
	SYS_ROLE_DEL: 'sys_role_del',
	SYS_ROLE_BIND: 'sys_role_bind',
	SYS_PERMS: 'sys_perms',
	SYS_PERMS_CREATE: 'sys_perms_create',
	SYS_PERMS_UPD: 'sys_perms_upd',
	SYS_PERMS_DEL: 'sys_perms_del',
	J<PERSON>_USER: 'jn_user',
	JN_ACT_LIST: 'jn_act_list',
	J<PERSON>_HOME_CHANGE: 'jn_home_change',
	J<PERSON>_OTHER_CHANGE: 'jn_other_change',
	JN_WORKS_EXAMINE: 'jn_works_examine',
	JN_DATA_VIEW: 'jn_data_view',
	JN_DATA_SEARCH: 'jn_data_search',
	JN_DATA_EXPORT: 'jn_data_export',
	YL_ACT_LIST: 'yili_act_list',
	SJF_ACT_LIST: 'sjf_act_list',
	HJ_ACT_LIST: 'royal_act_list',
	HJ_DATA_VIEW: 'royal_data_view',
	HJ_GENE_VIEW: 'royal_gene_view',
	BY_USER: 'by_user',
	BY_ACT_LIST: 'by_act_list',
	BY_GMV_LIST: 'by_gmv_list',
	BY_PEO_LIST: 'by_peo_list',
	BY_GOOD_LIST: 'by_good_list',
	BY_PRE_LIST: 'by_pre_list',
	ZST_ACTIVE: 'zst_active',
	ZST_HOME: 'zst_home',
	ZST_SETTING: 'zst_setting',
	ZST_VIPLIST: 'zst_viplist',
	ZST_VIPACTIVELIST: 'zst_vipactivelist',
	ZST_UP_ACTIVE: 'zst_up_active', //活动配置-活动规则-确认提交
	ZST_UP_VIP: 'zst_up_vip', //活动配置-生日/升级礼-保存
	ZST_EXCLUSIVE_ACTIVE: 'zst_exclusive_active', //活动配置-专享价商品添加
	ZST_UPGIFT: 'zst_upgift', //升级礼菜单
	ZST_UPGIFT_DATA: 'zst_upgift_data', //升级礼-活动数据
	ZST_UPGIFT_USER: 'zst_upgift_user', //升级礼-用户详情
	ZST_UPGIFT_EXPLORE: 'zst_upgift_explore', //升级礼-用户详情-导出
	ZST_BIRTHDAYGIFT: 'zst_birthday', //生日礼菜单
	ZST_BIRTHDAYGIFT_DATA: 'zst_birthday_data', //生日礼-活动数据
	ZST_BIRTHDAYGIFT_USER: 'zst_birthday_user', //生日礼-用户详情
	ZST_BIRTHDAYGIFT_EXPLORE: 'zst_birthday_explore', //生日礼-用户详情-导出
	SK2_ACTIVE: 'sk2_active', //SK2
	SK2_DATA_VIEW: 'sk2_data_view', //SK2-活动数据
	SK2_DATA_EXPLORE: 'sk2_data_explore', //SK2-导出
	SK2_CHANGE_STATUS: 'sk2_change_status', //SK2-修改状态
	SK2_CHANGE_PRIZE: 'sk2_change_prize', //SK2-中奖操作
	SK2_INPUT_NUMBER: 'sk2_input_number', //SK2-填写物流单号
	ZST_SEND_SCORE: 'zst_send_score', //资生堂-进店送积分
	FH_PRICE_LIST: 'fh_price_list', //飞鹤-专享价
	FH_PRICE_RECORD: 'fh_price_record', //飞鹤-专享价参与记录
	FH_NEW_CUSTOMER_CERTIFICATION: 'fh_new_customer_certification', //飞鹤-新客户认证
	FH_NEW_CUSTOMER_CERTIFICATION_EDIT_ACT: 'fh_new_customer_certification_edit_act', //飞鹤-新客户认证-编辑活动
	JBAT_ACTIVE: 'jbat_active', //佳贝艾特-活动管理
	JBAT_ACTIVE_LIST: 'jbat_active_list', //佳贝艾特-活动数据
	JBAT_EDIT_ACTIVE: 'jbat_edit_active', //佳贝艾特-创建活动
	JBAT_RECOED: 'jbat_record', //佳贝艾特-活动记录
	JBAT_REPORT: 'jbat_report', //佳贝艾特-数据报表
	JBAT_ACTIVE_1_0: 'jbat_active1_0', //佳贝艾特1.0-活动管理
	JBAT_ACTIVE_LIST_1_0: 'jbat_active_list_1_0', //佳贝艾特1.0-活动数据
	JBAT_EDIT_ACTIVE_1_0: 'jbat_edit_active_1_0', //佳贝艾特1.0-创建活动
	JBAT_RECOED_1_0: 'jbat_record_1_0', //佳贝艾特1.0-活动记录
	JBAT_REPORT_1_0: 'jbat_report_1_0', //佳贝艾特1.0-数据报表

	JBAT_ACTIVE_2_0: 'jbat_active2_0', //佳贝艾特2.0-活动管理
	JBAT_ACTIVE_LIST_2_0: 'jbat_active_list_2_0', //佳贝艾特2.0-活动数据
	JBAT_EDIT_ACTIVE_2_0: 'jbat_edit_active_2_0', //佳贝艾特2.0-创建活动
	JBAT_RECOED_2_0: 'jbat_record_2_0', //佳贝艾特2.0-活动记录
	JBAT_REPORT_2_0: 'jbat_report_2_0', //佳贝艾特2.0-数据报表
	JBAT_CAN_COLLECTION_GIFT: 'jbat_can_collection_gift', //佳贝艾特-集罐礼
	JBAT_EDIT_CAN_COLLECTION_GIFT: 'jbat_edit_can_collection_gift', //佳贝艾特-集罐礼-编辑活动
	JBAT_CAN_COLLECTION_GIFT_LIST: 'jbat_can_collection_gift_list', //佳贝艾特-集罐礼-活动数据
	JBAT_CAN_COLLECTION_GIFT_REPORT: 'jbat_can_collection_gift_report', //佳贝艾特-集罐礼-数据报表
	JBAT_CAN_COLLECTION_REPORT_SHOP: 'jbat_can_collection_report_shop', //佳贝艾特-集罐礼-店铺数据报表
	JBAT_REPURCHASE_GIFT: 'jbat_repurchase_gift', //佳贝艾特-复购礼
	JBAT_EDIT_REPURCHASE_GIFT: 'jbat_edit_repurchase_gift', //佳贝艾特-编辑复购礼
	JBAT_REPURCHASE_GIFT_LIST: 'jbat_repurchase_gift_list', //佳贝艾特-复购礼-活动数据
	JBAT_REPURCHASE_GIFT_REPORT: 'jbat_repurchase_gift_report', //佳贝艾特-复购礼-数据报表
};

export { PERM_KEY };
