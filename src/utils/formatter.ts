import { useDateFormat } from "@vueuse/core";
import type { TableColumnCtx } from "element-plus/es/components/table/src/table-column/defaults";

export const dateFormatter = (row: any, column: TableColumnCtx<any>, cellValue: any, index: number) => {
  const formatter = useDateFormat(new Date(cellValue), "YYYY-MM-DD");
  return formatter.value;
};

export const dateTimeFormatter = (row: any, column: TableColumnCtx<any>, cellValue: any, index: number) => {
  if(cellValue==null){
    return '-'
  }else{
    const formatter = useDateFormat(new Date(cellValue), "YYYY-MM-DD HH:mm:ss");
    return formatter.value;
  }
};

export const timeFormatter = (row: any, column: TableColumnCtx<any>, cellValue: any, index: number) => {
  const formatter = useDateFormat(new Date(cellValue), "HH:mm:ss");
  return formatter.value;
};
