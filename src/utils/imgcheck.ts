

import { ElMessage } from 'element-plus';
import { reactive } from 'vue'
// 图片尺寸
export const imgSizeCheck = (file: any, width: number, height: number) => {
    const imgsize = reactive({
        data: {
            widht: 0,
            height: 0,
        }
    })
    const isSize = new Promise((resolve: any, reject: any) => {
        const _URL = window.URL || window.webkitURL;
        let img: any = new Image()
        img.src = _URL.createObjectURL(file);
        img.onload = () => {
            imgsize.data.widht = img.width;
            imgsize.data.height = img.height;
            const valid = img.width === width && img.height === height;
            valid ? resolve() : reject();
        }
    }).then(() => {
        return true;
    }, () => {
        ElMessage.warning('上传文件的图片大小不合符标准,尺寸为' + width + '×' + height + '。当前图片尺寸为：' + imgsize.data.widht + 'x' + imgsize.data.height);
        return false;
    })
    return isSize;
};