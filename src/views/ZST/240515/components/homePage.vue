<template>
  <p>首页</p>
  <div style="display: flex">
    <div class="home-page">
      <el-form :label-position="labelPosition" label-width="100px" :model="formLabelAlign" style="max-width: 460px">
        <el-form-item label="上传背景">
          <div class="upload-bg">
            <el-upload class="avatar-uploader" :action="uploadUrl" :show-file-list="false"
              :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload"
              :headers="{[useUserStoreObj.tokenName]: useUserStoreObj.token}">
              <img class="img-upload" v-if="props.activeDetail.backgroundUrl" :src="props.activeDetail.backgroundUrl" />
              <el-icon v-else class="avatar-uploader-icon">
                <Plus />
              </el-icon>
            </el-upload>
            <div class="tip">
              <span style="font-size: 12px">上传首页背景图</span>
              <br>
              支持750*1540尺寸图片
            </div>
          </div>
        </el-form-item>
        <el-form-item label="添加活动热区">
          <div class="hot-area">
            <div class="hot-item" v-for="(it, index) in props.activeDetail.hotZone">
              <el-upload class="avatar-uploader-hot" :action="uploadUrl" :show-file-list="false" :on-success="(response, file, fileList) =>
                handleAvatarSuccessHot(
                  response,
                  file,
                  fileList,
                  index,
                )" :before-upload="beforeAvatarUploadHot"
                :headers="{[useUserStoreObj.tokenName]: useUserStoreObj.token}">
                <div v-if="it.hotZoneImageUrl">
                  <img :src="it.hotZoneImageUrl" class="hot-img" />
                  <div class="btn-group">
                    <div class="img-btn" @click.stop="setLink(it)">建立连接</div>
                    <div class="img-btn">更改</div>
                    <div class="img-btn" @click.stop="clearHot(index)">清除</div>
                  </div>
                </div>
                <div v-else style="margin-left: -15px; margin-top: 15px;">
                  +上传热区图片
                  <br>
                  <span style="font-size: 10px">支持730*324尺寸图片</span>
                </div>

              </el-upload>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="活动规则">
          <el-input type="textarea" placeholder="请输入内容" v-model="props.activeDetail.rule" />
        </el-form-item>
        <el-form-item label="页面名称">
          <el-input type="text" placeholder="请输入" v-model="props.activeDetail.publishName" maxlength="30" show-word-limit />
        </el-form-item>
        <!-- <el-form-item>
          <el-button type="primary" @click="saveSetting">确认提交</el-button>
        </el-form-item> -->
      </el-form>
    </div>
    <onlinePreview :radio1="radio1" :bgUrl="props.activeDetail.backgroundUrl" :ruleText="props.activeDetail.rule">
      <div v-if="radio1 === '活动规则'" class="rule-bg"
        :style="{ backgroundImage: `url(${props.activeDetail.backgroundUrl})`}">
        <div class="ovelay">
          <div class="rule-dialog">
            <div class="rule-text">{{ props.activeDetail.rule }}</div>
          </div>
        </div>
      </div>
      <div v-else>
        <div class="hot-area-box" v-if="props.activeDetail.hotZone.length>0">
          <div v-for="it in props.activeDetail.hotZone" class="hot-area-img">
            <img :src="it.hotZoneImageUrl" alt="">
          </div>
        </div>
      </div>
      <div class="radios-group">
        <el-radio-group v-model="radio1" @change="getRadioValue">
          <el-radio-button v-for="it in radioList" :label="it.label" />
        </el-radio-group>
      </div>
    </onlinePreview>
  </div>
  <el-dialog v-model="dialogVisible" title="Tips" width="500">
    <div style="margin-bottom: 10px;">编辑热区跳转链接</div>
    <el-input v-model="hotItemInfo.hotZoneJumpLink" placeholder="请输入内容" />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="checkLink">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormProps, UploadProps  } from 'element-plus'
import base from '/@/utils/request/base';
import onlinePreview from './onlinePreview.vue'
import { createOrUpdateActivityData, getActivityInfoData } from '/@/utils/request/api/ZST/activityApi'
import {useUserStore} from "/@/store/modules/user";
import { loadingStore } from "/@/store/modules/loading";

const loadingStoreObj = loadingStore();
const useUserStoreObj = useUserStore()
const labelPosition = ref<FormProps['labelPosition']>('top')
const uploadUrl = base.baseurl + "/shiseido/data/image"
// const uploadUrl = base.baseurl + "/img/uploadImage"

const props = defineProps({
  activeDetail: {
    type: Object,
  },
})

const getRadioValue = () => {
  console.log(radio1.value, 'radio1')
}
const formLabelAlign = reactive({
  // name: '',
  // region: '',
  // type: '',
})
const radio1 = ref('首页')
const dialogVisible = ref(false);

const radioList = ref([
  {
    value: 0,
    label: '首页',
  },
  {
    value: 1,
    label: '活动规则',
  },
])
// 建立链接
const hotItemInfo = ref();
const clearHot=(index:number)=>{
  ElMessageBox.confirm('确认清除该数据?')
    .then(() => {
      props.activeDetail.hotZone[index].hotZoneImageUrl = ''
      props.activeDetail.hotZone[index].hotZoneJumpLink = ''
    })
    .catch(() => {
      // catch error
    })
}
const setLink = (hot: any) => {
  dialogVisible.value = true;
  hotItemInfo.value = hot;
}
const checkLink = () =>{
  if (hotItemInfo.value.hotZoneJumpLink === '') {
    ElMessage.error('请输入跳转链接');
    return;
  }
  dialogVisible.value = false
};
const handleAvatarSuccess: UploadProps['onSuccess'] = (
    response,
    uploadFile
) => {
  props.activeDetail.backgroundUrl = response.data
  loadingStoreObj.hideLoading();
}
const handleAvatarSuccessHot: UploadProps['onSuccess'] = (
    response,
    uploadFile,
    fileList,
    index
) => {
  props.activeDetail.hotZone[index].hotZoneImageUrl = response.data
  loadingStoreObj.hideLoading();
}

const beforeAvatarUpload: UploadProps['beforeUpload'] = (file) => {
  loadingStoreObj.showLoading();
  const isSize = new Promise((resolve, reject) => {
    const width = 750;
    const height = 1540;
    const _URL = window.URL || window.webkitURL;
    const img = new Image();
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
    if (!isJpgOrPng) {
      ElMessage.error('仅支持上传jpg,png,jpeg格式的图片!');
      loadingStoreObj.hideLoading();
      return Promise.reject();
    }
    img.onload = function () {
      const valid = img.width === width && img.height === height;
      // eslint-disable-next-line prefer-promise-reject-errors
      valid ? resolve() : reject();
    };
    img.src = _URL.createObjectURL(file);
  }).then(
      () => file,
      () => {
        loadingStoreObj.hideLoading();
        ElMessage.error('上传的图片尺寸不符合规格，请重新上传');
        // eslint-disable-next-line prefer-promise-reject-errors
        return Promise.reject();
        // eslint-disable-next-line no-unreachable
        return false;
      },
  );
  return isSize;
};
const beforeAvatarUploadHot: UploadProps['beforeUpload'] = (file) => {
  loadingStoreObj.showLoading();
  const isSize = new Promise((resolve, reject) => {
    const width = 730;
    const height = 324;
    const _URL = window.URL || window.webkitURL;
    const img = new Image();
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
    if (!isJpgOrPng) {
      ElMessage.error('仅支持上传jpg,png,jpeg格式的图片!');
      loadingStoreObj.hideLoading();
      return Promise.reject();
    }
    img.onload = function () {
      const valid = img.width === width && img.height === height;
      // eslint-disable-next-line prefer-promise-reject-errors
      valid ? resolve() : reject();
    };
    img.src = _URL.createObjectURL(file);
  }).then(
      () => file,
      () => {
        loadingStoreObj.hideLoading();
        ElMessage.error('上传的图片尺寸不符合规格，请重新上传');
        // eslint-disable-next-line prefer-promise-reject-errors
        return Promise.reject();
        // eslint-disable-next-line no-unreachable
        return false;
      },
  );
  return isSize;
};

</script>
<style lang="less" scoped>
.upload-bg {
  width: 153px;
  height: 92px;
  border-radius: 10px;
  background-color: #c4c4c4;
  box-sizing: border-box;
  padding-top: 13px;
  text-align: center;
}
.tip {
  margin-top: 8px;
  font-size: 9px;
  line-height: 1.3;
}
.hot-area {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/99625/28/42937/30954/65dc3c8bFcb13eb3b/10d83b21c548a98b.png) no-repeat;
  background-size: 100%;
  width: 250px;
  height: 514px;
  border-radius: 10px;
  box-sizing: border-box;
  padding-top: 110px;
}
.hot-item {
  width: 240px;
  height: 95px;
  margin: 0 auto 5px auto;
  border-radius: 8px;
  background-color: #c4c4c4;
}
::v-deep.avatar-uploader .el-upload {
  border: 3px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  //position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  width: 38px;
  height: 38px;
  display: block;
  margin: 0 auto;
}

::v-deep.avatar-uploader-hot .el-upload {
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  width: 100%;
  height: 100%;
  display: block;
  margin: 0 auto;
  text-align: center;
  line-height: 1.2;
  box-sizing: border-box;
  padding-top: 5px;
}

::v-deep.el-icon.avatar-uploader-icon {
  font-size: 23px;
  color: #fff;
  width: 34px;
  height: 34px;
  text-align: center;
}
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.home-page {
  padding: 20px;
  background-color: #f6f6f6;
  width: 50%;
}
.hot-area-box {
  margin-top: 140px;
}
.hot-area-img {
  width: 320px;
  height: 135px;
  margin: 10px auto;
  border-radius: 10px;
  //background-color: #3a6df9;
}
.hot-area-img img {
  width: 320px;
}
.hot-img {
  width: 150px;
  display: block;
  margin: 0 auto 5px auto;
  //margin-bottom: 15px;
}
.btn-group {
  display: flex;
  width: 155px;
  margin: 0 auto;
  justify-content: space-between;
}
.img-btn {
  width: 45px;
  line-height: 18px;
  border-radius: 5px;
  background-color: #fff;
  text-align: center;
  cursor: pointer;
  font-size: 10px;
  color: #000;
}
.img-upload {
  width: 33px;
}
.rule-bg {
  /*background: url(//img20.360buyimg.com/imgzone/jfs/t1/93973/5/44157/81376/65b7016fF767fe553/b60ad26dcd6e6e4d.jpg) no-repeat;*/
  background-repeat: no-repeat;
  background-size: 100%;
  width: 335px;
  height: 700px;
  border-radius: 50px;
  box-sizing: border-box;
  position: relative;
}
.ovelay {
  width: 100%;
  height: 100%;
  position: absolute;
  width: 335px;
  height: 700px;
  top: 0;
  border-radius: 50px;
  background-color: rgba(0, 0, 0, 0.7);
}
.rule-dialog {
  position: absolute;
  z-index: 9;
  background: url("//img10.360buyimg.com/imgzone/jfs/t1/157987/22/41977/16394/65dc5dceF999f6244/d7fbeb955aff87f5.png") no-repeat;
  background-size: 100%;
  width: 270px;
  height: 450px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  box-sizing: border-box;
  padding-top: 55px;
}
.rule-text {
  width: 250px;
  height: 380px;
  margin: 0 auto;
  overflow-y: scroll;
  color: #fff;
  font-size: 13px;
}
.rule-text::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
.radios-group {
  position: absolute;
  margin-left: 60px;
  bottom: 0;
}

</style>
