<template>
  <el-tabs
    :tab-position="tabPosition"
    v-model="currentIndex"
    class="demo-tabs"
    @tab-change="handleClick"
  >
    <!-- <div style="display: flex; flex-direction: row-reverse">
      <el-button type="primary" @click="saveMemberInfo">保存</el-button>
    </div> -->
    <el-tab-pane label="粉卡会员">
      <div style="display: flex; flex-wrap: wrap; padding-top: 20px">
        <div
          class="upload-img"
          v-for="(it, index) in pinkCardImgList"
          :key="it.imgUrl + index"
        >
          {{ it.title }}
          <upload
            @handleAvatarSuccess="handleAvatarSuccess"
            :imageUrl="it.imgUrl"
            :imgTip="it.title"
            :imgWidth="it.width"
            :imgHeight="it.height"
            :index="index"
          ></upload>
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="银卡会员">
      <div style="display: flex; flex-wrap: wrap; padding-top: 20px">
        <div class="upload-img" v-for="(it, index) in silverCardImgList" :key="it.imgUrl">
          {{ it.title }}
          <upload
            @handleAvatarSuccess="handleAvatarSuccess"
            :imageUrl="it.imgUrl"
            :imgTip="it.title"
            :imgWidth="it.width"
            :imgHeight="it.height"
            :index="index"
          ></upload>
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="金卡会员">
      <div style="display: flex; flex-wrap: wrap; padding-top: 20px">
        <div class="upload-img" v-for="(it, index) in goldCardImgList" :key="it.imgUrl">
          {{ it.title }}
          <upload
            @handleAvatarSuccess="handleAvatarSuccess"
            :imageUrl="it.imgUrl"
            :imgTip="it.title"
            :imgWidth="it.width"
            :imgHeight="it.height"
            :index="index"
          ></upload>
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="白金卡会员">
      <div style="display: flex; flex-wrap: wrap; padding-top: 20px">
        <div
          class="upload-img"
          v-for="(it, index) in platinumCardImgList"
          :key="it.imgUrl"
        >
          {{ it.title }}
          <upload
            @handleAvatarSuccess="handleAvatarSuccess"
            :imageUrl="it.imgUrl"
            :imgTip="it.title"
            :imgWidth="it.width"
            :imgHeight="it.height"
            :index="index"
          ></upload>
        </div>
      </div>
    </el-tab-pane>
    <div>
      <div>当前奖品剩余库存：{{ props.memberPageInfo.giftNum }}</div>
      <div>
        需添加的库存数量：
        <a-input
          style="width: 200px"
          v-model:value="inventoryInfo[Number(currentIndex)].giftAddNum"
          @input="handleInput"
          maxlength="5"
          placeholder="请输入"
          allowClear
          show-count
        ></a-input>
      </div>
    </div>
    <!-- <div v-if="(Number(currentIndex) + 3) < 5 && props.actType == 2">添加活动奖品
      <br>
      <el-select clearable v-model="prizeValue" class="m-2" placeholder="请选择" style="width: 240px">
        <el-option v-for="item in prizeList" :key="item.exclusivePriceGoodsId" :label="item.exclusivePriceGoodsName"
          :value="item.exclusivePriceGoodsId">
          <span style="float: left">{{ `【${item.exclusivePriceGoodsName}】` }}</span>
          <span style="float: right; color: var(--el-text-color-secondary);font-size: 13px;">
            {{ item.exclusivePriceGoodsId }}
          </span>
        </el-option>
      </el-select>
    </div> -->
  </el-tabs>
</template>
<script setup lang="ts">
import { ref, defineProps, defineEmits, computed, watch } from "vue";
import upload from "./upload.vue";
import {
  pinkCardImgList,
  silverCardImgList,
  goldCardImgList,
  platinumCardImgList,
  inventoryInfo,
} from "../data/birthdayData";
import { createOrUpdateMemberData } from "/@/utils/request/api/ZST/activityApi";
import { ElMessage } from "element-plus";
// import { prizeList } from "/@/views/ZST/240515/data/func";
import { loadingStore } from "/@/store/modules/loading";

const loadingStoreObj = loadingStore();
const imageUrl = ref("");
const props = defineProps({
  actType: {
    type: Number,
    default: 0,
  },
  memberPageInfo: {
    type: Object,
    default: () => {},
  },
  monitorValue: {
    type: String,
    default: "",
  },
  currentTab: {
    type: String,
    default: "",
  },
});
const prizeValue = ref("");
const tabPosition = ref("left");

const handleInput = (e: any) => {
  // 使用正则表达式限制只能输入正整数
  inventoryInfo.value[Number(currentIndex.value)].giftAddNum = e.target.value.replace(
    /\D/g,
    ""
  );
  props.memberPageInfo.giftAddNum =
    inventoryInfo.value[Number(currentIndex.value)].giftAddNum;
  emits("getInfo", props.memberPageInfo);
};
const test = computed(() => {
  switch (currentIndex.value) {
    case "0":
      return pinkCardImgList.value;
      break;
    case "1":
      return silverCardImgList.value;
      break;
    case "2":
      return goldCardImgList.value;
      break;
    case "3":
      return platinumCardImgList.value;
      break;
    default:
      return pinkCardImgList.value;
  }
});

const currentIndex = ref("0");
const handleClick = (name: any) => {
  emits("searchInfo", name);
  test.value[0].imgUrl = props.memberPageInfo.backgroundImageUrl;
  test.value[1].imgUrl = props.memberPageInfo.equityImageUrl;
  test.value[2].imgUrl = props.memberPageInfo.equityButtonUrl;
  test.value[3].imgUrl = props.memberPageInfo.backgroundButtonUrlA;
  test.value[4].imgUrl = props.memberPageInfo.addressButton;
  if (Number(currentIndex.value) > 1) {
    test.value[5].imgUrl = props.memberPageInfo.popImgUrl;
  }
  prizeValue.value = props.memberPageInfo.exclusivePriceGoodsId;
  inventoryInfo.value[Number(currentIndex.value)].giftAddNum =
    props.memberPageInfo.giftAddNum == 0 ? null : props.memberPageInfo.giftAddNum;
};
watch(
  () => props.memberPageInfo,
  () => {
    test.value[0].imgUrl = props.memberPageInfo.backgroundImageUrl;
    test.value[1].imgUrl = props.memberPageInfo.equityImageUrl;
    test.value[2].imgUrl = props.memberPageInfo.equityButtonUrl;
    test.value[3].imgUrl = props.memberPageInfo.backgroundButtonUrlA;
    test.value[4].imgUrl = props.memberPageInfo.addressButton;
    if (Number(currentIndex.value) > 1) {
      test.value[5].imgUrl = props.memberPageInfo.popImgUrl;
    }
    prizeValue.value = props.memberPageInfo.exclusivePriceGoodsId;
    inventoryInfo.value[Number(currentIndex.value)].giftAddNum =
      props.memberPageInfo.giftAddNum == 0 ? null : props.memberPageInfo.giftAddNum;
  },
  { deep: true }
);
watch(
  () => props.monitorValue,
  () => {
    currentIndex.value = "0";
  },
  { deep: true }
);
watch(
  () => props.currentTab,
  () => {
    currentIndex.value = props.currentTab as string;
  },
  { deep: true }
);
const handleAvatarSuccess = (res: any, index: number) => {
  pushInfo(index, res.data);

  switch (currentIndex.value) {
    case "0":
      pinkCardImgList.value[index].imgUrl = res.data;
      break;
    case "1":
      silverCardImgList.value[index].imgUrl = res.data;
      break;
    case "2":
      goldCardImgList.value[index].imgUrl = res.data;
      break;
    case "3":
      platinumCardImgList.value[index].imgUrl = res.data;
      break;
    default:
      console.log("");
  }
  loadingStoreObj.hideLoading();
};

const emits = defineEmits(["searchInfo", "getInfo"]);
const loading = ref(false);

const pushInfo = (index: any, url: string) => {
  switch (index) {
    case "0":
      props.memberPageInfo.backgroundImageUrl = url;
      break;
    case "1":
      props.memberPageInfo.equityImageUrl = url;
      break;
    case "2":
      props.memberPageInfo.equityButtonUrl = url;
      break;
    case "3":
      props.memberPageInfo.backgroundButtonUrlA = url;
      break;
    case "4":
      props.memberPageInfo.addressButton = url;
      break;
    case "5":
      props.memberPageInfo.popImgUrl = url;
      break;

    default:
      break;
  }
  console.log(props.memberPageInfo);
  emits("getInfo", props.memberPageInfo);
};
const saveMemberInfo = () => {
  // const index = prizeList.value?.findIndex((it) => it.exclusivePriceGoodsId === prizeValue.value)
  // if ((prizeList.value.length < 1 || index == -1 || !prizeList.value[index].exclusivePriceId) && (Number(currentIndex.value) + 3) < 5) {
  //   ElMessage.error('请添加活动奖品')
  //   return
  // }

  const params = {
    backgroundImageUrl: test.value[0].imgUrl,
    equityImageUrl: test.value[1].imgUrl,
    backgroundButtonUrlB: test.value[2].imgUrl,
    backgroundButtonUrlA: test.value[3].imgUrl,
    equityButtonUrl: test.value[2].imgUrl,
    addressButton: test.value[4].imgUrl,
  };
  if (loading.value) return;
  loading.value = true;
  createOrUpdateMemberData({
    hotZoneType: props.actType,
    id: props.memberPageInfo.id ? props.memberPageInfo.id : "",
    memberType: Number(currentIndex.value) + 3,
    exclusivePriceId: null,
    exclusivePriceGoodsId: null,
    exclusivePriceGoodsName: null,
    // exclusivePriceId: index == -1?'':prizeList.value[index].exclusivePriceId,
    // exclusivePriceGoodsId: prizeValue.value,
    // exclusivePriceGoodsName: index == -1?'':prizeList.value[index].exclusivePriceGoodsName,
    ...params,
  }).then((res: any) => {
    if (res) {
      ElMessage.success("保存成功");
      emits("searchInfo", currentIndex.value);
    }
    loading.value = false;
  });
};
</script>
<style scoped lang="scss">
:deep(.el-tabs__active-bar.is-left) {
  left: 0;
}
:deep(.el-tabs__nav-wrap.is-left::after) {
  left: 0;
}
:deep(.el-tabs__nav) {
  width: 100%;
}
:deep(.el-tabs__item) {
  width: 100% !important;
}
:deep(.el-tabs__item.is-left) {
  text-align: center;
  justify-content: center;
}
:deep(.el-tabs__nav-scroll) {
  background-color: var(--el-border-color-light);
}
:deep(.el-tabs__item.is-active) {
  background-color: #fff;
}
:deep(.el-tabs__content) {
  background-color: #f0f2f5;
  padding: 20px;
}
.upload-img {
  justify-content: space-between;
  width: 270px;
  margin-bottom: 30px;
}
</style>
