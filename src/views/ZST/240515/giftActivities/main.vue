<template>
  <div class="zst-main">
    <a-date-picker v-model:value="time" :allowClear="false" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
      @change="getDayData" />

    <div style="display: flex">
      <PieCharts :ref="(el: any) => (dropitemRefs[index] = el)" v-for="(item, index) in pieChartData"
        :generalTitle="item.generalTitle" :pieData="item.pieData" :increase="item.increase"
        style="margin-right: 40px;" />
    </div>

    <div class="filter-container">
      <a-range-picker v-model:value="checkDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="changeDate"
        :allowClear="false" />

      <a-radio-group v-model:value="typeIndex" style="position: absolute;right: 15%;" button-style="solid">
        <a-radio-button value="0">折线图</a-radio-button>
        <a-radio-button value="1">表格</a-radio-button>
      </a-radio-group>

    </div>
    <LineChart v-if="typeIndex == '0'" ref="line" :titleData="titleData" :xAxisData="xAxisData" :pvData="pvData"
      :uvData="uvData" />

    <a-table v-else :columns="columns" :data-source="data" bordered style="margin-top: 20px;">
      <template #summary>
        <a-table-summary-row>
          <a-table-summary-cell align="center">Total</a-table-summary-cell>
          <a-table-summary-cell align="center">
            <a-typography-text>{{ totals.totalUV }}</a-typography-text>
          </a-table-summary-cell>
          <a-table-summary-cell align="center">
            <a-typography-text>{{ totals.totalPV }}</a-typography-text>
          </a-table-summary-cell>
        </a-table-summary-row>
      </template>
    </a-table>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, onMounted } from 'vue';
import type { Dayjs } from 'dayjs';
import PieCharts from '../components/PieCharts.vue';
import LineChart from '../components/lineChart.vue';
import type { TableColumnsType } from 'ant-design-vue';
import { getHomeLineChartsData, getHomeDateData } from '/@/utils/request/api/ZST/activityApi'
import moment from 'moment';
import { dayjs } from 'element-plus';

const dropitemRefs: any = ref([])

const time = ref<Dayjs>(dayjs().format('YYYY-MM-DD'));
const typeIndex = ref<string>('0');

const columns = ref<TableColumnsType>([
  {
    title: '时间',
    dataIndex: 'date',
    align: 'center'
  },
  {
    title: 'UV',
    dataIndex: 'uv',
    align: 'center'
  },
  {
    title: 'PV',
    dataIndex: 'pv',
    align: 'center'
  },
]);
const data = ref([])

const totals = computed(() => {
  let totalUV = 0;
  let totalPV = 0;

  data.value.forEach(({ uv, pv }) => {
    totalUV += uv;
    totalPV += pv;
  });
  return { totalUV, totalPV };
});

//饼图数据
const pieChartData = ref([
  {
    generalTitle: '活动UV',    //标题
    pieData: [{
      value: 0,//总数据
      name: '总UV',
      color: '#1954f5'
    },
    {
      value: 0,//当日数据
      name: '当日UV',
      color: '#ff004e'
    },
    {
      value: 0,//昨日数据
      name: '昨日UV',
      color: '#171f30'
    }],
    increase: 0,    //当日涨幅数
  },
  {
    generalTitle: '活动PV',    //标题
    pieData: [{
      value: 0,//总数据
      name: '总PV',
      color: '#1954f5'
    },
    {
      value: 0,//当日数据
      name: '当日PV',
      color: '#ff004e'
    },
    {
      value: 0,//昨日数据
      name: '昨日PV',
      color: '#171f30'
    }],
    increase: 0,    //当日涨幅数
  },
])

//获取饼图数据
const getDayData = async () => {
  let param = {
    date: time.value
  }
  const res: any = await getHomeDateData(param);
  pieChartData.value[0].pieData[0].value = res.allUv
  pieChartData.value[0].pieData[1].value = res.todayUv
  pieChartData.value[0].pieData[2].value = res.yesterdayUv
  pieChartData.value[0].increase = res.uvGrowthRate
  pieChartData.value[1].pieData[0].value = res.allPv
  pieChartData.value[1].pieData[1].value = res.todayPv
  pieChartData.value[1].pieData[2].value = res.yesterdayPv
  pieChartData.value[1].increase = res.pvGrowthRate
  setTimeout(() => {
    dropitemRefs.value[0].initEchart()
    dropitemRefs.value[1].initEchart()
  }, 10);
}

// 折线图默认传参  当前日期-前一个月
const startDate = ref(moment(new Date().setTime(new Date().getTime() - 3600 * 1000 * 24 * 30)).format('YYYY-MM-DD'))
const endDate = ref(moment(new Date().getTime()).format('YYYY-MM-DD'))

type RangeValue = [Dayjs, Dayjs];
// 时间选择器默认时间
const checkDate = ref<RangeValue>([
  dayjs(new Date().getTime() - 3600 * 1000 * 24 * 30),
  dayjs(new Date().getTime())
]);
// 时间范围-选择时间
const changeDate = (val: any) => {
  startDate.value = val[0]
  endDate.value = val[1]
  getLineChartsDataFun()
}

// 传给折线图的数据
const titleData = ref(['UV', 'PV'])
const xAxisData = ref([])  //x轴坐标
const uvData = ref([])  //uv数据
const pvData = ref([])  //pv数据
const line = ref<any>(null);

// 获取折线图数据
const getLineChartsDataFun = async () => {
  let param = {
    startDate: startDate.value,
    endDate: endDate.value
  }
  const res: any = await getHomeLineChartsData(param);
  data.value = res
  xAxisData.value = []
  uvData.value = []
  pvData.value = []
  res.forEach((item: any) => {
    xAxisData.value.push(item.date)
    uvData.value.push(item.uv)
    pvData.value.push(item.pv)
  })
  setTimeout(() => {
    line.value?.echart()
  }, 10);

}


onMounted(() => {
  getDayData()
  getLineChartsDataFun()
})

</script>
<style lang="less" scoped>
.pie-chart {
  display: flex;
  margin-top: 20px;
  box-sizing: border-box;
  position: relative;
}

.right-data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  //align-items: center;
}

.data-icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
}

.data-value {
  font-size: 18px;
  font-weight: bold;
}

.title {
  font-weight: bold;
  font-size: 18px;
  color: #3a6df9;
}

.data-type {
  display: flex;
  width: 200px;
  height: 30px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  padding: 3px;
  position: absolute;
  top: 0;
  right: 15%;
}

.type-item {
  text-align: center;
  line-height: 1.8;
  flex: 1;
  cursor: pointer;
}

.hight-light {
  background-color: #fff;
  border-radius: 7px;
  //line-height: 1.8;
  width: 100px;
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.2);
}

.filter-container {
  position: relative;
}
</style>
