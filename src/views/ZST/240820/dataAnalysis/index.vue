<template>
    <div class="zst-main">
        <div style="display: flex;justify-content: space-between">
            <a-date-picker v-model:value="clickDate" :allowClear="false" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                @change="getDayData" :disabledDate="disabledDate" />
            <a-button @click="router.go(-1)">返回列表</a-button>
        </div>
        <!-- 饼图 -->
        <div style="display: flex;">
            <PieCharts :ref="(el: any) => (dropitemRefs[index] = el)" v-for="(item, index) in pieChartData"
                :generalTitle="item.generalTitle" :pieData="item.pieData" :increase="item.increase"
                style="margin-right: 40px;" :style="{ width: calcWidth }" />
        </div>
        <!-- 折线图/表格 -->
        <div style="padding-bottom: 30px;">
            <div class="filter-container">
                <a-range-picker v-model:value="checkDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                    @change="changeDate" :allowClear="false" :disabledDate="disabledDate" />
                <a-button type="primary" v-if="tabPosition == '1'" style="margin-left: 20px;" @click="exportData">导出</a-button>
                <a-radio-group v-model:value="tabPosition" style="position: absolute;right: 15%;" button-style="solid">
                    <a-radio-button value="0">折线图</a-radio-button>
                    <a-radio-button value="1">表格</a-radio-button>
                </a-radio-group>
            </div>

            <LineChart v-if="tabPosition == '0'" ref="line" :titleData="titleData" :xAxisData="xAxisData"
                :secret="secret" />

            <a-table v-else :columns="columns" :row-key="record => record.date" :data-source="tableData" bordered
                style="margin-top: 20px;">
                <template #summary>
                    <a-table-summary-row>
                        <a-table-summary-cell align="center">Total</a-table-summary-cell>
                        <a-table-summary-cell align="center">
                            <a-typography-text>{{ totals.totalUV }}</a-typography-text>
                        </a-table-summary-cell>
                        <a-table-summary-cell align="center">
                            <a-typography-text>{{ totals.totalPV }}</a-typography-text>
                        </a-table-summary-cell>
                        <a-table-summary-cell align="center">
                            <a-typography-text>{{ totals.totalOpenCard }}</a-typography-text>
                        </a-table-summary-cell>
                        <a-table-summary-cell align="center">
                            <a-typography-text>{{ totals.totalDrawGiftCount }}</a-typography-text>
                        </a-table-summary-cell>
                    </a-table-summary-row>
                </template>
            </a-table>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { getTotalData, getActiveData, activeDataExport } from '/@/utils/request/api/ZST/240820/activityApi'
import { dayjs } from 'element-plus';
import type { Dayjs } from 'dayjs';
import { TableColumnsType } from 'ant-design-vue';
import { downloadBlob } from '/@/utils/request/api/commonFun'

import { useRouter } from 'vue-router';

const router = useRouter();

const queryData = JSON.parse(localStorage.getItem('queryData'))

const calcWidth = computed(() => {
    const percentagePart = (100 / pieChartData.value.length).toFixed(2);
    return `calc(${percentagePart}% - 50px)`;
});

const dropitemRefs: any = ref([])

const tableData = ref([])   //表格数据

const totals = computed(() => {
    let totalUV = 0;
    let totalPV = 0;
    let totalDrawGiftCount = 0;
    let totalOpenCard = 0;

    tableData.value.forEach(({ uv, pv, drawGiftCount, openCard }) => {
        totalUV += uv;
        totalPV += pv;
        totalDrawGiftCount += drawGiftCount;
        totalOpenCard += openCard;
    });
    return { totalUV, totalPV, totalOpenCard, totalDrawGiftCount };
});

const columns = ref<TableColumnsType>([
    {
        title: '时间',
        dataIndex: 'date',
        align: 'center'
    },
    {
        title: 'uv',
        dataIndex: 'uv',
        align: 'center'
    },
    {
        title: 'pv',
        dataIndex: 'pv',
        align: 'center'
    },
    {
        title: '入会人数',
        dataIndex: 'openCard',
        align: 'center'
    },
    {
        title: '领取人数',
        dataIndex: 'drawGiftCount',
        align: 'center'
    },
]);

//饼图数据
const pieChartData = ref([
    {
        generalTitle: '活动UV',    //标题
        pieData: [{
            value: 0,//总数据
            name: '总UV',
            color: '#1954f5'
        },
        {
            value: 0,//当日数据
            name: '当日UV',
            color: '#ff004e'
        },
        {
            value: 0,//昨日数据
            name: '昨日UV',
            color: '#171f30'
        }],
        increase: 0,    //当日涨幅数
    },
    {
        generalTitle: '活动PV',    //标题
        pieData: [{
            value: 0,//总数据
            name: '总PV',
            color: '#1954f5'
        },
        {
            value: 0,//当日数据
            name: '当日PV',
            color: '#ff004e'
        },
        {
            value: 0,//昨日数据
            name: '昨日PV',
            color: '#171f30'
        }],
        increase: 0,    //当日涨幅数
    },
    {
        generalTitle: '入会人数',    //标题
        pieData: [{
            value: 0,//总数据
            name: '总入会人数',
            color: '#1954f5'
        },
        {
            value: 0,//当日数据
            name: '当日入会人数',
            color: '#ff004e'
        },
        {
            value: 0,//昨日数据
            name: '昨日入会人数',
            color: '#171f30'
        }],
        increase: 0,    //当日涨幅数
    },
    {
        generalTitle: '领取人数',    //标题
        pieData: [{
            value: 0,//总数据
            name: '总领取人数',
            color: '#1954f5'
        },
        {
            value: 0,//当日数据
            name: '当日领取人数',
            color: '#ff004e'
        },
        {
            value: 0,//昨日数据
            name: '昨日领取人数',
            color: '#171f30'
        }],
        increase: 0,    //当日涨幅数
    },
])

//单个时间默认值
const clickDate = ref<Dayjs>(dayjs(new Date(queryData.activeStartTime)).format('YYYY-MM-DD'));

const disabledDate = (current) => {
    let tooLate = false
    let tooEarly = false
    tooLate = dayjs(current.format('YYYY-MM-DD'), 'YYYY-MM-DD').valueOf() < dayjs(queryData.activeStartTime, 'YYYY-MM-DD').valueOf();
    tooEarly = dayjs(current.format('YYYY-MM-DD'), 'YYYY-MM-DD').valueOf() > dayjs(queryData.activeEndTimeactiveEndTime, 'YYYY-MM-DD').valueOf()

    // return !!tooEarly || !!tooLate;
    return false
}

//获取饼图数据
const getDayData = async () => {
    let param = {
        date: clickDate.value,
        activityId: queryData.activityId
    }
    const res: any = await getTotalData(param);
    pieChartData.value[0].pieData[0].value = res.allUv
    pieChartData.value[0].pieData[1].value = res.todayUv
    pieChartData.value[0].pieData[2].value = res.yesterdayUv
    pieChartData.value[0].increase = res.uvGrowthRate

    pieChartData.value[1].pieData[0].value = res.allPv
    pieChartData.value[1].pieData[1].value = res.todayPv
    pieChartData.value[1].pieData[2].value = res.yesterdayPv
    pieChartData.value[1].increase = res.pvGrowthRate

    pieChartData.value[2].pieData[0].value = res.allOpenCard
    pieChartData.value[2].pieData[1].value = res.todayOpenCard
    pieChartData.value[2].pieData[2].value = res.yesterdayOpenCard
    pieChartData.value[2].increase = res.openCardGrowthRate??0

    pieChartData.value[3].pieData[0].value = res.allDrawGift
    pieChartData.value[3].pieData[1].value = res.todayDrawGift
    pieChartData.value[3].pieData[2].value = res.yesterdayDrawGift
    pieChartData.value[3].increase = res.drawGiftGrowthRate??0

    dropitemRefs.value[0].initEchart()
    dropitemRefs.value[1].initEchart()
    dropitemRefs.value[2].initEchart()
    dropitemRefs.value[3].initEchart()
}

// 切换折线图/表格
const tabPosition = ref('0')

// 折线图默认传参  当前日期-前一个月
const startDate = ref(queryData.activeStartTime.slice(0, 10))
const endDate = ref(queryData.activeEndTime.slice(0, 10))

type RangeValue = [Dayjs, Dayjs];

// 时间选择器默认时间
const checkDate = ref<RangeValue>([
    dayjs(new Date(queryData.activeStartTime)),
    dayjs(new Date(queryData.activeEndTime))
]);
// 时间范围-选择时间
const changeDate = (val: any) => {
    startDate.value = val[0]
    endDate.value = val[1]
    getLineChartsDataFun()
}

// 传给折线图的数据
const titleData = ref(['UV', 'PV', '入会人数', '领取人数'])
const xAxisData = ref([])  //x轴坐标
const uvData = ref([])  //uv数据
const pvData = ref([])  //pv数据
const drawGiftCountData = ref([])   //领取人数数据
const receiveGiftCountData = ref([])   //入会人数数据
const line = ref<any>(null);
const secret = ref([] as any)
// 获取折线图数据
const getLineChartsDataFun = async () => {
    let param = {
        startDate: startDate.value,
        endDate: endDate.value,
        activityId: queryData.activityId
    }
    const res: any = await getActiveData(param);
    tableData.value = res
    xAxisData.value = []
    uvData.value = []
    pvData.value = []
    drawGiftCountData.value = []
    receiveGiftCountData.value = []
    res.forEach((item: any) => {
        xAxisData.value.push(item.date)
        uvData.value.push(item.uv)
        pvData.value.push(item.pv)
        drawGiftCountData.value.push(item.drawGiftCount)
        receiveGiftCountData.value.push(item.openCard)
    })
    
    secret.value = [
        {
            name: 'PV',
            type: 'line', //图标类型
            data: pvData.value,
        },
        {
            name: 'UV',
            type: 'line',
            data: uvData.value
        },
        {
            name: '入会人数',
            type: 'line',
            data: receiveGiftCountData.value
        },
        {
            name: '领取人数',
            type: 'line',
            data: drawGiftCountData.value
        },
    ]
    setTimeout(() => {
        line.value?.echart()
    }, 10);

}

const exportData = async() => {
    let param = {
        startDate: startDate.value,
        endDate: endDate.value,
        activityId: queryData.activityId
    }
    const res: any = await activeDataExport(param)
    downloadBlob(res.data, res, `${queryData.activeName}活动数据.xlsx`)

}

onMounted(() => {
    getLineChartsDataFun()
    getDayData()
})

</script>
<style lang="less" scoped>
.filter-container {
    position: relative;
}

.birthday-card {
    width: calc(100% / 3 - 50px);
    height: 150px;
    border: 1px solid #ccc;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    position: relative;
}

.birthday-title {
    color: #ccc;
    font-size: 18px;
}

.birthday-data {
    font-weight: bold;
    font-size: 24px;
}

.birthday-tips {
    margin-top: 20px;
    font-size: 16px;
    color: #ccc;
}

.birthday-tips-number {
    color: #91cc75;
    font-weight: bold;
}
</style>