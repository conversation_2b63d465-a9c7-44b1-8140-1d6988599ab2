import { reactive } from "vue";
import { dayjs } from 'element-plus';
import type { Dayjs } from 'dayjs';

// 活动主页面
export const homeInfoList = reactive([
    {
        title: '主页面设置',
        children: [
            {
                title: '页面背景图',
                imgUrl: '',
                size: 1,
                width: 750,
                height: 2180,
            },
            {
                title: '活动规则按钮',
                imgUrl: '',
                width: 41,
                height: 144,
                size: 1,
            },
        ]
    },
    {
        title: '促销机制展示图',
        children: [
            {
                title: '',
                imgUrl: '',
                width: 720,
                height: 302,
                type: '',//跳转链接
                size: 1,
            },
        ]
    },
    {
        title: '底部按钮',
        children: [
            {
                title: '',
                imgUrl: '',
                width: 336,
                height: 60,
                type: '',//跳转链接
                size: 1,
            },
        ]
    },
])
// 货架商品
export const goodsInfoList = reactive([
    {
        title: '货架商品01',
        imgUrl: '',
        width: 350,
        height: 430,
        goodsId: '',
        size: 1,
    },
    {
        title: '货架商品02',
        imgUrl: '',
        width: 350,
        height: 430,
        goodsId: '',
        size: 1,
    },
    {
        title: '货架商品03',
        imgUrl: '',
        width: 350,
        height: 430,
        goodsId: '',
        size: 1,
    },
    {
        title: '货架商品04',
        imgUrl: '',
        width: 350,
        height: 430,
        goodsId: '',
        size: 1,
    },
])
// 活动弹窗
export const popList = reactive([
    {
        title: '领取成功-弹窗',
        imgUrl: '',
        width: 516,
        height: 572,
        type: '',//跳转链接
        size: 1,
    },
    {
        title: '您已领取过-弹窗',
        imgUrl: '',
        width: 516,
        height: 572,
        type: '',//跳转链接
        size: 1,
    },
    {
        title: '您在其他渠道领取过-弹窗',
        imgUrl: '',
        width: 516,
        height: 572,
        type: '',//跳转链接
        size: 1,
    },
    {
        title: '等级不满足-弹窗',
        imgUrl: '',
        width: 516,
        height: 572,
        type: '',//跳转链接
        size: 1,
    },
    {
        title: '活动规则-弹窗',
        imgUrl: '',
        width: 516,
        height: 572,
        size: 1,
    },
    {
        title: '非会员引导入会弹窗',
        imgUrl: '',
        width: 516,
        height: 572,
        size: 1,
    },
])
// 活动信息配置
export const activityData = reactive({
    activityName: '',
    startTime: '',
    endTime: '',
    grade: null,
    awardName: '',
    totalStock: null,
    point: null,
    pointExpireTime: '',
    rule: '',
    ruleTextColor:''
})
// 选中的活动信息
export const queryData = reactive({
    activityId:'',
    activeStartTime: '',
    activeEndTime: '',
    activeName:'',
    status: null
})

export function resetData(){
    homeInfoList.forEach((item,index) => {
        item.children.forEach(child => {
            child.imgUrl = '';
            if(index>0){
                child.type = '';
            }
        })
    })
    goodsInfoList.forEach(item => {
        item.imgUrl = '';
        item.goodsId = '';
    })
    popList.forEach((item,index) => {
        item.imgUrl = '';
        if (index < popList.length-2){
            item.type = '';
        }
    })
    Object.keys(activityData).forEach(key => {
        activityData[key] = '';
    })
}