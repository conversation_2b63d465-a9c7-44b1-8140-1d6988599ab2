<template>
  <div class="container">
    <div class="left-container">
      <div
        class="item"
        v-for="(item, index) in componentList"
        :key="index + 'componentList'"
        draggable="true"
        @dragstart="dragStart(item, $event)"
      >
        {{ item.title }}
      </div>
    </div>
    <div
      class="middle-container"
      @dragover.prevent
      @drop="dragDrop($event)"
      style="border: 1px solid #ccc; padding: 10px; height: 100%"
    >
      拖拽左侧元素到这里
      <div
        v-for="(config, index) in selectedConfigs"
        :key="index + 'selectedConfigs'"
        draggable="true"
        @dragstart="dragStart(config, $event, index)"
        @dragover="dragOver(index)"
        style="display: flex; align-items: center"
      >
        <span
          class="selected-item"
          :class="checkedIndex == index ? 'selected-item-active' : ''"
          @click="checkedIndex = index"
        >
          <!-- banner图 -->
          <span
            class="selected-item"
            v-if="config.type == 'banner'"
            :style="{
              width: !config.width ? '100%' : config.width + 'px',
              height: !config.height ? '30px' : config.height + 'px',
              backgroundImage: `url(${config.imgUrl})`,
            }"
          ></span>
          <!-- 搜索组件 -->
          <searchComponent v-if="config.type == 'search'" />
        </span>
        <span @click="delItem(index)">删</span>
        <a-button
          v-if="selectedConfigs.length > 1"
          :disabled="index == 0"
          @click="exchangeItem(index, index - 1)"
          >上移</a-button
        >
        <a-button
          v-if="selectedConfigs.length > 1"
          :disabled="index == selectedConfigs.length - 1"
          @click="exchangeItem(index, index + 1)"
          >下移</a-button
        >
      </div>
    </div>
    <!-- 设置配置信息 -->
    <div class="right-container">
      <settingContent
        @deleteImage="deleteImage"
        @uploadSuccess="uploadSuccess"
        @updateSelectedConfigs="updateSelectedConfigs"
        :checkedType="checkedType"
        :selectedConfigs="selectedConfigs"
        :checkedIndex="checkedIndex"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Ref, onMounted, reactive, ref, computed, unref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { componentList } from "./componentData";
import settingContent from "./components/settingContent.vue";
import searchComponent from "./components/searchComponent.vue";

const router = useRouter();

interface itemData {
  title: String;
  type: String;
  imgUrl: String;
  toUrl: String;
  width?: String | Number;
  height?: String | Number;
  dataList?: Array;
}

const checkedIndex = ref(null); // 当前选中的配置项索引
const selectedConfigs = ref([]); // 已选配置
let draggedItem: any = null; // 被拖拽的元素
let draggedIndex: number | null = null; // 被拖拽元素的索引
let targetIndex: number | null = null; // 放置目标元素的索引
const checkedType = ref("");

// 图片上传成功
const uploadSuccess = (url) => {
  selectedConfigs.value[checkedIndex.value].imgUrl = url;
};
const deleteImage = () => {
  selectedConfigs.value[checkedIndex.value].imgUrl = "";
};
const updateSelectedConfigs = (value) => {
  const { width, height, toUrl } = value;

  if (width != undefined) {
    selectedConfigs.value[checkedIndex.value].width = width;
  }
  if (height != undefined) {
    selectedConfigs.value[checkedIndex.value].height = height;
  }
  if (toUrl != undefined) {
    selectedConfigs.value[checkedIndex.value].toUrl = toUrl;
  }
};

// 拖拽开始事件
const dragStart = (item: any, event: DragEvent, index?: number) => {
  draggedItem = JSON.parse(JSON.stringify(item));
  draggedIndex = index;
  event.dataTransfer?.setData("text/plain", JSON.stringify(item));
};

// 拖拽结束事件
const dragOver = (index: number) => {
  targetIndex = index;
};

// 放置事件
const dragDrop = (event: DragEvent) => {
  if (draggedItem && (draggedIndex === null || draggedIndex === undefined)) {
    // 从左侧容器拖拽到中间容器
    selectedConfigs.value.push(draggedItem);
    checkedIndex.value = selectedConfigs.value.length - 1;
  }
  if (
    draggedItem &&
    draggedIndex !== null &&
    draggedIndex !== undefined &&
    targetIndex !== undefined &&
    targetIndex !== null
  ) {
    // 在中间容器内拖拽
    const newIndex = targetIndex;
    const oldIndex = draggedIndex;

    if (oldIndex !== newIndex) {
      const itemToMove = selectedConfigs.value[oldIndex];
      selectedConfigs.value.splice(oldIndex, 1);
      selectedConfigs.value.splice(newIndex, 0, itemToMove);
    }
    checkedIndex.value = targetIndex;
  }
  console.log("componentList", componentList);
  if (checkedIndex.value == null) {
    checkedType.value = null;
  } else {
    checkedType.value = selectedConfigs.value[checkedIndex.value].type;
  }
  draggedItem = null;
  draggedIndex = null;
  targetIndex = null;
};

// 删除
const delItem = (index) => {
  selectedConfigs.value.splice(index, 1);
  if (index == checkedIndex.value) {
    if (index == 0) {
      if (selectedConfigs.value.length > 0) {
        checkedIndex.value = 0;
      } else {
        checkedIndex.value = null;
      }
    } else {
      checkedIndex.value = index - 1;
    }
  } else if (index < checkedIndex.value) {
    checkedIndex.value = checkedIndex.value - 1;
  }
  if (checkedIndex.value == null) {
    checkedType.value = null;
  } else {
    checkedType.value = selectedConfigs.value[checkedIndex.value].type;
  }
};

// 交换两个元素的位置
const exchangeItem = (index1, index2) => {
  const temp = selectedConfigs.value[index1];
  selectedConfigs.value[index1] = selectedConfigs.value[index2];
  selectedConfigs.value[index2] = temp;
  checkedIndex.value = index2;
};

watch(
  () => checkedIndex.value,
  () => {
    console.log(checkedIndex.value, "checkedIndex.value");

    if (checkedIndex.value == null) {
      checkedType.value = null;
    } else {
      checkedType.value = selectedConfigs.value[checkedIndex.value].type;
    }
  }
);

onMounted(() => {
  // 初始化逻辑
  console.log(componentList);
});
</script>

<style lang="less" scoped>
.container {
  display: flex;
  align-items: stretch;
  height: 100vh;
}

.left-container {
  display: flex;
  flex-wrap: wrap;
  width: 20%;
  justify-content: space-evenly;
}

.right-container {
  flex: 1;
  margin: 0 5px;
  padding: 10px;
  border: 1px solid #ccc;
}

.item {
  width: 70px;
  height: 70px;
  border: 1px solid #ccc;
  text-align: center;
  line-height: 70px;
}

.middle-container {
  flex: 2;
  position: relative;
  overflow-y: auto;
}

.selected-item {
  cursor: move;
  margin: 5px;
  padding: 5px;
  border: 1px solid #ccc;
  display: flex;
  width: 50%;
  justify-content: space-between;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.selected-item-active {
  color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}
</style>
