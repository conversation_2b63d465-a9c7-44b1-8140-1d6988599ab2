<template>
	<div class="">
		<div class="label">基本数据</div>
		<a-form layout="inline">
			<a-form-item label="日期" class="marg-b-20">
				<a-range-picker :allowClear="false" v-model:value="formData.rangeDate" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
			</a-form-item>
			<a-form-item class="marg-b-20">
				<a-button type="primary" style="margin-bottom: 20px" @click="searchAct">搜索</a-button>
				<a-button type="primary" style="margin-bottom: 20px" @click="exportData">导出</a-button>
			</a-form-item>
		</a-form>
		<a-table :columns="columns" :data-source="tableData" :pagination="false" bordered :loading="isLoading">
			<!-- <template #bodyCell="{ record, column }"> </template> -->
		</a-table>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { basicData, exportBasicData } from '/@/utils/request/api/JBAT/newCustomerOneApi';
import { dayjs } from 'element-plus';
import { message } from 'ant-design-vue';
import { downloadExcel } from '/@/utils';

const router = useRouter();
const route = useRoute();

const isLoading = ref(false);
const pageInfo = ref({
	pageNo: 1,
	pageSize: 10,
	toTal: 0
});

const columns = [
	{
		title: 'pv',
		dataIndex: 'pv',
		key: 'pv'
	},
	{
		title: 'uv',
		dataIndex: 'uv',
		key: 'uv'
	},
	{
		title: '入会人数',
		dataIndex: 'openCardMember',
		key: 'openCardMember'
	},
	{
		title: '新客礼1.0领取人数',
		dataIndex: 'newCustomerOneNum',
		key: 'newCustomerOneNum'
	},
	{
		title: '老客礼1.0领取人数',
		dataIndex: 'oldCustomerOneNum',
		key: 'oldCustomerOneNum'
	},
];

const formData = reactive({
	rangeDate: [route.query.startTime ? dayjs(route.query.startTime) : '', dayjs().subtract(1, 'day')]
});
const tableData = ref([]);

const getList = async () => {
	try {
		isLoading.value = true;
		const query = {
			activityId: route.query.id,
			startTime: formData.rangeDate[0] ? dayjs(formData.rangeDate[0]).format('YYYY-MM-DD') : '',
			endTime: formData.rangeDate[1] ? dayjs(formData.rangeDate[1]).format('YYYY-MM-DD') : ''
		};
		const res = await basicData(query);
		tableData.value = [res];
		// pageInfo.value.total = res.total;
		isLoading.value = false;
	} catch (error) {
		isLoading.value = false;
	}
};

const searchAct = () => {
	pageInfo.value.pageNo = 1;
	getList();
};

const exportData = () => {
	const query = {
		activityId: route.query.id,
		startTime: formData.rangeDate[0] ? dayjs(formData.rangeDate[0]).format('YYYY-MM-DD') : '',
		endTime: formData.rangeDate[1] ? dayjs(formData.rangeDate[1]).format('YYYY-MM-DD') : ''
	};
	exportBasicData(query).then((data: any) => {
		downloadExcel(data.data, '基本数据');
	});
};

getList();
</script>

<style scoped lang="scss">
.page {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;
}
.shop-select {
	width: 400px;
}
.marg-b-20 {
	margin-bottom: 20px;
}
.label {
	font-size: 20px;
	font-weight: 600;
	margin-bottom: 20px;
}
</style>
