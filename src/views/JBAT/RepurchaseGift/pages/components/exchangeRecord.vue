<template>
	<div class="zst-main">
		<div>
			<a-form layout="inline">
				<a-form-item label="用户昵称" class="marg-b-20">
					<a-input v-model:value="formData.nickname" allowClear style="width: 200px" placeholder="请输入用户昵称"></a-input>
				</a-form-item>
				<a-form-item label="openId" class="marg-b-20">
					<a-input v-model:value="formData.openId" allowClear style="width: 200px" placeholder="请输入用户PIN"></a-input>
				</a-form-item>
				<!-- <a-form-item label="Xid">
					<a-input v-model:value="formData..xid" allowClear style="width: 200px" placeholder="请输入Xid"></a-input>
				</a-form-item> -->
				<a-form-item label="奖品名称" class="marg-b-20">
					<a-input v-model:value="formData.prizeName" allowClear style="width: 200px" placeholder="请输入奖品名称"></a-input>
				</a-form-item>
				<a-form-item label="领取状态" class="marg-b-20">
					<a-select v-model:value="formData.status" style="width: 200px">
						<a-select-option :value="''">全部</a-select-option>
						<a-select-option :value="'1'">领取成功</a-select-option>
						<a-select-option :value="'2'">领取失败</a-select-option>
					</a-select>
				</a-form-item>
				<a-form-item label="领取时间" class="marg-b-20">
					<a-range-picker v-model:value="formData.rangeDate" show-time format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss" />
				</a-form-item>
				<a-form-item class="marg-b-20">
					<a-button type="primary" style="margin-bottom: 20px" @click="searchAct">搜索</a-button>
					<a-button type="primary" style="margin-bottom: 20px" @click="exportData">导出</a-button>
				</a-form-item>
			</a-form>
		</div>
		<a-table :columns="columns" :data-source="tableData" :pagination="false" bordered :loading="isLoading">
			<template #bodyCell="{ column, index: rowIndex }">
				<template v-if="column.key === 'receiveInfo'">
					<a-button type="link" @click="handleView(tableData[rowIndex])" :disabled="tableData[rowIndex].addressStatus === '未填写'">{{ tableData[rowIndex].addressStatus }}</a-button>
				</template>
			</template>
			<!-- <template #bodyCell="{ record, column }"> </template> -->
		</a-table>
		<div class="page">
			<a-pagination v-model:current="pageInfo.pageNo" v-model:pageSize="pageInfo.pageSize" :total="pageInfo.total" @change="getList" showSizeChanger />
		</div>

		<a-modal v-model:visible="visible" title="收件信息" :footer="false">
			<div>
				<div class="info-item">
					<span class="label">姓名：</span>
					<span class="value">{{ modalData.realName }}</span>
				</div>
				<div class="info-item">
					<span class="label">手机号：</span>
					<span class="value">{{ modalData.mobile }}</span>
				</div>
				<div class="info-item">
					<span class="label">省：</span>
					<span class="value">{{ modalData.province }}</span>
				</div>
				<div class="info-item">
					<span class="label">市：</span>
					<span class="value">{{ modalData.city }}</span>
				</div>
				<div class="info-item">
					<span class="label">区：</span>
					<span class="value">{{ modalData.county }}</span>
				</div>
				<div class="info-item">
					<span class="label">详细地址：</span>
					<span class="value">{{ modalData.address }}</span>
				</div>
			</div>
		</a-modal>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { activityReceiveRecordPage, exportActivityReceiveRecord } from '/@/utils/request/api/JBAT/repurchaseGift';
import { dayjs } from 'element-plus';
import { message } from 'ant-design-vue';
import { downloadExcel } from '/@/utils';

const router = useRouter();
const route = useRoute();
const visible = ref(false);
const modalData = ref({});

const isLoading = ref(false);
const pageInfo = ref({
	pageNo: 1,
	pageSize: 10,
	total: 0
});

const columns = [
	{
		title: '用户昵称',
		dataIndex: 'nickname',
		key: 'nickname'
	},
	{
		title: 'openId',
		dataIndex: 'openId',
		key: 'openId'
	},
	{
		title: '领取奖品',
		dataIndex: 'prizeName',
		key: 'prizeName'
	},
	{
		title: '领取数量',
		dataIndex: 'recriveCount',
		key: 'recriveCount'
	},
	{
		title: '领取时间',
		dataIndex: 'receiveTime',
		key: 'receiveTime'
	},
	{
		title: '领取状态',
		dataIndex: 'receiveStatus',
		key: 'receiveStatus'
	},
	{
		title: '备注',
		dataIndex: 'errorMessage',
		key: 'errorMessage'
	},
	{
		title: '满足领取条件订单号',
		dataIndex: 'orderId',
		key: 'orderId'
	},
	{
		title: '收件信息',
		dataIndex: 'receiveInfo',
		key: 'receiveInfo'
	}
];

const formData = reactive({
	nickname: '',
	openId: '',
	mobile: '',
	prizeTypeName: '',
	prizeName: '',
	status: '',
	rangeDate: []
});
const tableData = ref([]);

const getList = async () => {
	try {
		isLoading.value = true;
		if (!formData.rangeDate) {
			formData.rangeDate = [];
		}
		const query = {
			activityId: route.query.id,
			...formData,
			startTime: !!formData.rangeDate[0] ? dayjs(formData.rangeDate[0]).format('YYYY-MM-DD HH:mm:ss') : '',
			endTime: !!formData.rangeDate[1] ? dayjs(formData.rangeDate[1]).format('YYYY-MM-DD HH:mm:ss') : '',
			pageNo: pageInfo.value.pageNo,
			pageSize: pageInfo.value.pageSize
		};
		const res: any = await activityReceiveRecordPage(query);
		tableData.value = res.records;
		pageInfo.value.total = res.total;
		isLoading.value = false;
	} catch (error) {
		console.error(error);
		isLoading.value = false;
	}
};

const searchAct = () => {
	pageInfo.value.pageNo = 1;
	getList();
};

const exportData = () => {
	const query = {
		activityId: route.query.id,
		...formData,
		startTime: formData.rangeDate[0] ? dayjs(formData.rangeDate[0]).format('YYYY-MM-DD HH:mm:ss') : '',
		endTime: formData.rangeDate[1] ? dayjs(formData.rangeDate[1]).format('YYYY-MM-DD HH:mm:ss') : ''
	};
	exportActivityReceiveRecord(query).then((data: any) => {
		downloadExcel(data.data, '领取记录');
	});
};

const handleView = (record: any) => {
	console.log(record);
	modalData.value = record;
	visible.value = true;
};

getList();
</script>

<style scoped lang="scss">
.page {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;
}
.shop-select {
	width: 400px;
}
.marg-b-20 {
	margin-bottom: 20px;
}
.info-item {
	margin-bottom: 12px;
	display: flex;
}
.label {
	width: 80px;
	color: #666;
}
.value {
	color: #333;
	flex: 1;
}
</style>
