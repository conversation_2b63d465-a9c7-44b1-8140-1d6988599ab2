<template>
	<div class="zst-main">
		<a-tabs v-model:activeKey="activeKey">
			<a-tab-pane key="1" :tab="`报名记录`">
				<SignRecord />
			</a-tab-pane>
			<a-tab-pane key="2" tab="领取记录">
				<exchangeRecord />
			</a-tab-pane>
			<!--			<a-tab-pane key="2" tab="出生证绑定记录">-->
			<!--				<BindingRecord />-->
			<!--			</a-tab-pane>-->
		</a-tabs>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import SignRecord from './components/SignRecord.vue';
import exchangeRecord from './components/exchangeRecord.vue';
// import BindingRecord from './components/BindingRecord.vue';

const activeKey = ref('1');
const route = useRoute();
</script>

<style scoped lang="scss"></style>
