<template>
	<div class="zst-main">
		<a-tabs v-model:activeKey="activeKey">
			<a-tab-pane key="1" tab="活动日报数据">
				<BasicData />
			</a-tab-pane>
			<a-tab-pane key="2" tab="活动数据">
				<DetailData />
			</a-tab-pane>
			<!--			<a-tab-pane key="2" tab="出生证绑定记录">-->
			<!--				<BindingRecord />-->
			<!--			</a-tab-pane>-->
		</a-tabs>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import BasicData from './components/BasicData.vue';
import DetailData from './components/DetailData.vue';

const activeKey = ref('1');
</script>

<style scoped lang="scss"></style>
