<template>
	<div class="zst-main">
		<div>
			<a-form layout="inline">
				<a-form-item label="活动名称">
					<a-input v-model:value="activityName" allowClear style="width: 200px" placeholder="请输入活动名称"></a-input>
				</a-form-item>
				<a-form-item label="活动ID">
					<a-input v-model:value="activityId" allowClear style="width: 200px" placeholder="请输入活动ID"></a-input>
				</a-form-item>
				<a-form-item>
					<a-button type="primary" style="margin-bottom: 20px" @click="searchAct">搜索</a-button>
					<a-button type="primary" style="margin-bottom: 20px" @click="createActivity">创建活动</a-button>
				</a-form-item>
			</a-form>
		</div>
		<a-table :columns="columns" :data-source="tableData" :pagination="false" bordered :loading="isLoading">
			<template #bodyCell="{ record, column }">
				<template v-if="column.key === 'activityInfo'">
					<div>活动ID：{{ record.activityId }}</div>
					<div v-if="evn === 'test'">
						活动链接：{{ `shopId=${record.shopId}&activityId=${record.activityId}`
						}}<a-button type="link" @click="copyLink(`shopId=${record.shopId}&activityId=${record.activityId}`)">复制</a-button>
					</div>
					<div v-else>
						<div v-if="record.activityUrl">活动链接：{{ record.activityUrl }}<a-button type="link" @click="copyLink(record.activityUrl)">复制</a-button></div>
						<div v-else>暂无链接</div>
					</div>
				</template>
				<template v-if="column.key === 'shopId'">
					<div>{{ shopList.find((item) => item.value === record.shopId.toString())?.label }}</div>
				</template>
				<template v-else-if="column.key === 'time'">
					<div>
						开始时间：{{ record.startTime }} <br />
						结束时间：{{ record.endTime }}
					</div>
				</template>
				<template v-else-if="column.key === 'scene'">
					<div>
						{{
							record.sceneList
								.map((item) => {
									const scene = sceneOptions.find((option) => option.value === item);
									return scene ? scene.label : item;
								})
								.join(',')
						}}
					</div>
				</template>
				<template v-else-if="column.key === 'operation'">
					<a-button class="table-btn" type="link" @click="editActivityInfo(record)">编辑</a-button>
					<a-button class="table-btn" type="link" @click="toRecord(record)">记录</a-button>
					<a-button class="table-btn" type="link" @click="toReport(record)">报表</a-button>
					<a-button class="table-btn" type="link" @click="selectScene(record)">选择场景</a-button>
					<a-button class="table-btn" type="link" @click="toDelete(record)">删除</a-button>
				</template>
			</template>
		</a-table>
		<div class="page">
			<a-pagination v-model:current="pageInfo.pageNo" v-model:pageSize="pageInfo.pageSize" :total="pageInfo.toTal" @change="getList" showSizeChanger />
		</div>

		<a-modal v-model:visible="visible" title="创建活动" @ok="handleOk" :ok-button-props="{ disabled: selectShopId === '' }">
			<a-form-item label="选择店铺">
				<a-select class="shop-select" ref="select" :options="shopList" v-model:value="selectShopId" @change="handleChange"> </a-select>
			</a-form-item>
		</a-modal>

		<a-modal v-model:visible="selectSceneVisible" title="选择场景" @ok="changeSceneId">
			<a-checkbox-group v-model:value="selectRow.sceneList" :options="sceneOptions" />
		</a-modal>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { getPageList, deleteActivity, addScene } from '/@/utils/request/api/JBAT/repurchaseGift';
import { message, Modal } from 'ant-design-vue';
import { useUserStore } from '/@/store/modules/user.ts';

const router = useRouter();
const isLoading = ref(false);
const pageInfo = ref({
	pageNo: 1,
	pageSize: 10,
	toTal: 0
});
const visible = ref(false);
const selectShopId = ref('');
const useUserStoreObj = useUserStore();
const shopId = ref('');

const shopList = ref<any>([]);
const evn = ref('test');

const userListMap = [
	{
		userName: 'superAdmin',
		label: '超级管理员',
		shopId: ''
	},
	{
		userName: 'superAdmin2',
		label: '超级管理员',
		shopId: ''
	},
	{
		userName: 'superAdmin3',
		label: '超级管理员',
		shopId: ''
	},
	{
		userName: '徐宁',
		label: '超级管理员',
		shopId: ''
	},
	{
		userName: '薛佳丽',
		label: '超级管理员',
		shopId: ''
	},
	{
		userName: '刘津佐',
		label: '超级管理员',
		shopId: ''
	},
	{
		userName: 'JBAT-new',
		label: '超级管理员',
		shopId: ''
	},
	{
		userName: 'zstAdmin',
		label: '超级管理员',
		shopId: ''
	},
	{
		userName: 'JBAT02',
		label: '超级管理员',
		shopId: ''
	},
	{
		userName: 'KABRITA旗舰店',
		label: 'KABRITA旗舰店',
		shopId: '407961'
	},
	{
		userName: '佳贝艾特母婴旗舰店',
		label: '佳贝艾特母婴旗舰店',
		shopId: '38566226'
	},
	{
		userName: '佳贝艾特成人粉旗舰店',
		label: '佳贝艾特成人粉旗舰店',
		shopId: '99250902'
	},
	{
		userName: '佳贝艾特营养奶粉旗舰店',
		label: '佳贝艾特营养奶粉旗舰店',
		shopId: '125644821'
	},
	{
		userName: '佳贝艾特婴童用品旗舰店',
		label: '佳贝艾特婴童用品旗舰店',
		shopId: '169039348'
	},
	{
		userName: '佳贝艾特母婴用品旗舰店',
		label: '佳贝艾特母婴用品旗舰店',
		shopId: '62361844'
	},
	{
		userName: '海普诺凯1897官方旗舰店',
		label: '海普诺凯1897官方旗舰店',
		shopId: '37702451'
	},
	{
		userName: '能立多旗舰店',
		label: '能立多旗舰店',
		shopId: '40842709'
	}
];

const sceneOptions = [
	{ label: '默认', value: '0' },
	{ label: '店铺官方页', value: '027012' },
	{ label: '直播间小雪花', value: '023010' },
	{ label: '扫码', value: '990001' }
];

const columns = [
	{
		title: '活动信息',
		key: 'activityInfo'
	},
	{
		title: '活动名称',
		dataIndex: 'activityName',
		key: 'activityName'
	},
	{
		title: '店铺',
		dataIndex: 'shopId',
		key: 'shopId'
	},
	{
		title: '活动时间',
		key: 'time'
	},
	// {
	// 	title: '活动状态',
	// 	dataIndex: 'status',
	// 	key: 'status'
	// },
	// {
	// 	title: '商品id',
	// 	dataIndex: 'exclusivePriceGoodsId',
	// 	key: 'exclusivePriceGoodsId'
	// },
	// {
	// 	title: '购买数量',
	// 	dataIndex: 'userBuyLimit',
	// 	key: 'userBuyLimit'
	// },
	{ title: '投放场景', key: 'scene' },
	{ title: '操作', key: 'operation' }
];

const activityName = ref('');
const activityId = ref('');
const tableData = ref([]);

const getList = async () => {
	try {
		isLoading.value = true;
		const currentUrl = window.location.href;
		if (currentUrl.includes('127.0.0.1') || currentUrl.includes('localhost') || currentUrl.includes('cjtd-b-web-test.lucidata.cn')) {
			evn.value = 'test';
		} else {
			evn.value = 'prod';
		}
		userListMap.map((item) => {
			if (item.userName === useUserStoreObj.userInfo.username) {
				shopId.value = item.shopId;
				console.log(shopId.value);
				if (shopId.value) {
					shopList.value = [{ label: `${item.label}新客礼2.0`, value: item.shopId }];
				} else {
					shopList.value = [
						{ label: 'KABRITA旗舰店新客礼2.0', value: '407961' },
						{ label: '佳贝艾特母婴旗舰店新客礼2.0', value: '38566226' },
						{ label: '佳贝艾特成人粉旗舰店新客礼2.0', value: '99250902' },
						{ label: '佳贝艾特营养奶粉旗舰店新客礼2.0', value: '125644821' },
						{ label: '佳贝艾特婴童用品旗舰店新客礼2.0', value: '169039348' },
						{ label: '佳贝艾特母婴用品旗舰店新客礼2.0', value: '62361844' },
						{ label: '海普诺凯1897官方旗舰店新客礼2.0', value: '37702451' },
						{ label: '能立多旗舰店新客礼2.0', value: '40842709' }
					];
				}
			}
		});
		const res: any = await getPageList({ activityName: activityName.value, shopId: shopId.value, activityId: activityId.value || null, ...pageInfo.value });
		tableData.value = res.records;
		pageInfo.value.toTal = res.total;
		isLoading.value = false;
	} catch (error: any) {
		isLoading.value = false;
	}
};

const searchAct = () => {
	pageInfo.value.pageNo = 1;
	getList();
};

const createActivity = () => {
	visible.value = true;
};

const handleChange = (value) => {
	selectShopId.value = value;
};

const handleOk = () => {
	router.push({ path: '/jbat/EditRepurchaseGift', query: { type: 'create', shopId: selectShopId.value } });
	visible.value = false;
	selectShopId.value = '';
};

const editActivityInfo = (record) => {
	router.push({ path: '/jbat/EditRepurchaseGift', query: { type: 'edit', id: record.activityId, shopId: record.shopId } });
};

const toRecord = (record) => {
	router.push({ path: '/jbat/RepurchaseGiftRecord', query: { id: record.activityId, startTime: record.startTime, endTime: record.endTime } });
};

const toReport = (record) => {
	router.push({ path: '/jbat/RepurchaseGiftReport', query: { id: record.activityId, startTime: record.startTime, endTime: record.endTime } });
};

const dialogVisible = ref(false);
const toDelete = (record) => {
	Modal.confirm({
		title: '提醒',
		content: '确定删除该活动吗？',
		okText: '确定',
		cancelText: '取消',
		onOk() {
			try {
				isLoading.value = true;
				deleteActivity({ activityId: record.activityId });
				setTimeout(() => {
					message.success('删除成功');
					dialogVisible.value = false;
					getList();
				}, 1000);
				isLoading.value = false;
			} catch (error: any) {
				console.log(error);
				isLoading.value = false;
				message.error(error.message);
			}
		},
		onCancel() {
			dialogVisible.value = false;
		}
	});
};

const copyText = (text) => {
	return new Promise<void>((resolve) => {
		const tag = document.createElement('input');
		tag.setAttribute('id', 'cp_hgz_input_new');
		tag.value = text;
		document.getElementsByTagName('body')[0].appendChild(tag);
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		// @ts-ignore
		document.getElementById('cp_hgz_input_new').select();
		document.execCommand('copy');
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		// @ts-ignore
		document.getElementById('cp_hgz_input_new').remove();
		resolve();
	});
};

const copyLink = async (text) => {
	await copyText(text);
	message.success('复制成功');
};

getList();

const selectRow = ref<any>(null);
const selectSceneVisible = ref(false);
const selectScene = (record) => {
	selectRow.value = JSON.parse(JSON.stringify(record));
	selectSceneVisible.value = true;
};
const changeSceneId = async () => {
	try {
		const res = await addScene({ activityId: selectRow.value.activityId, sceneIds: selectRow.value.sceneList, shopId: selectRow.value.shopId });
		message.success('修改成功');
		selectSceneVisible.value = false;
		getList();
	} catch (error) {}
};
</script>

<style scoped lang="scss">
.page {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;
}
.shop-select {
	width: 400px;
}
.table-btn {
	padding: 0 !important;
	&:last-child {
		margin-right: 0;
	}
}
</style>
