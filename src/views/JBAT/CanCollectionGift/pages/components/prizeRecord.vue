<template>
	<div class="zst-main">
		<div>
			<a-form layout="inline">
				<a-form-item label="活动ID" class="marg-b-20">
					<a-input v-model:value="formData.activityId" readonly style="width: 200px" placeholder="请输入活动ID"></a-input>
				</a-form-item>
				<a-form-item label="openId" class="marg-b-20">
					<a-input v-model:value="formData.openId" allowClear style="width: 200px" placeholder="请输入openId"></a-input>
				</a-form-item>
				<a-form-item label="领取时间" class="marg-b-20">
					<a-range-picker v-model:value="formData.rangeDate" show-time format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss" />
				</a-form-item>
				<a-form-item class="marg-b-20">
					<a-button type="primary" style="margin-bottom: 20px" @click="searchAct">搜索</a-button>
					<a-button type="primary" style="margin-bottom: 20px" @click="exportData">导出</a-button>
				</a-form-item>
			</a-form>
		</div>
		<a-table :columns="columns" :data-source="tableData" :pagination="false" bordered :loading="isLoading">
			<!-- <template #bodyCell="{ record, column }"> </template> -->
		</a-table>
		<div class="page">
			<a-pagination v-model:current="pageInfo.pageNo" v-model:pageSize="pageInfo.pageSize" :total="pageInfo.toTal" @change="getList" showSizeChanger />
		</div>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { gainFlowRecord, gainFlowRecordExport } from '/@/utils/request/api/JBAT/canCollectionGiftApi';
import { downloadExcel } from '/@/utils';

const isLoading = ref(false);
const route = useRoute();
const pageInfo = ref({
	pageNo: 1,
	pageSize: 10,
	toTal: 0
});

const columns = [
	{
		title: '时间',
		dataIndex: 'createTime',
		align: 'center',
		key: 'createTime'
	},
	{
		title: '用户昵称',
		dataIndex: 'nickname',
		align: 'center',
		key: 'nickname'
	},
	{
		title: '用户PIN',
		dataIndex: 'openId',
		align: 'center',
		key: 'openId'
	},
	{
		title: '获取类型',
		dataIndex: 'orderTypeDesc',
		align: 'center',
		key: 'orderTypeDesc'
	},
	{
		title: '获得数量',
		dataIndex: 'changeNum',
		align: 'center',
		key: 'changeNum'
	},
	{
		title: '订单号',
		dataIndex: 'orderId',
		align: 'center',
		key: 'orderId'
	},
	{
		title: '符合条件商品',
		dataIndex: 'productId',
		align: 'center',
		key: 'productId'
	},
	{
		title: '增加后的总罐数/金额',
		dataIndex: 'total',
		key: 'total',
		align: 'center',
		customRender: ({ text, record }) => {
			if (text === null) return '-';
			return text;
		}
	}
];

const formData = reactive({
	activityId: route.query.activityId,
	openId: '',
	rangeDate: []
});
const tableData = ref([]);

const getList = async () => {
	console.log('🚀 ~ getList ~ formData.rangeDate:', formData.rangeDate);
	try {
		isLoading.value = true;
		if (!formData.rangeDate) {
			formData.rangeDate = [];
		}
		const { rangeDate, ...rest } = formData;
		const query = {
			...rest,
			activityId: route.query.activityId, // 优先使用路由参数
			startTime: formData.rangeDate[0] || '',
			endTime: formData.rangeDate[1] || '',
			pageNo: pageInfo.value.pageNo,
			pageSize: pageInfo.value.pageSize
		};
		const res = await gainFlowRecord(query);
		tableData.value = res.records;
		pageInfo.value.total = res.total;
		isLoading.value = false;
	} catch (error) {
		console.error(error);
		isLoading.value = false;
	}
};

const searchAct = () => {
	pageInfo.value.pageNo = 1;
	getList();
};

const exportData = () => {
	const { rangeDate, ...rest } = formData;
	const query = {
		...rest,
		activityId: route.query.activityId || formData.activityId, // 优先使用路由参数
		startTime: formData.rangeDate[0] || '',
		endTime: formData.rangeDate[1] || ''
	};
	gainFlowRecordExport(query).then((data: any) => {
		downloadExcel(data.data, '领取记录');
	});
};

getList();
</script>

<style scoped lang="scss">
.page {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;
}
.shop-select {
	width: 400px;
}
.marg-b-20 {
	margin-bottom: 20px;
}
</style>
