<template>
	<div class="page">
		<h1 data-t="404">404</h1>
		<p>当前页面不存在,<router-link to="/">点击回到首页</router-link></p>
	</div>
</template>

<script setup lang="ts"></script>

<style lang="less" scoped>
.page {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	bottom: 0;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

h1 {
	text-align: center;
	width: 100%;
	font-size: 6rem;
	animation: shake 0.6s ease-in-out infinite alternate;
}
@keyframes shake {
	0% {
		transform: translate(-1px);
	}
	10% {
		transform: translate(2px, 1px);
	}
  30% {
    transform: translate(-3px,2px);
  }
  50% {
    transform: translate(-3px,1px);
  }
}

h1:before{
  content: attr(data-t);
  position: absolute;
  left: 50%;
  transform: translate(-50%,0.34em);
  height: 0.1em;
  line-height: 0.5em;
  width: 100%;
  animation: scan 0.5s ease-in-out 275ms infinite alternate , glitch-anim 0.3s ease-in-out infinite alternate;
  overflow: hidden;
  opacity: 0.7;
}

@keyframes glitch-anim {
	0% {
		clip: rect(32px, 9999px, 28px, 0);
	}
  10% {
		clip: rect(13px, 9999px, 37px, 0);
	}
  20% {
		clip: rect(45px, 9999px, 33px, 0);
	}
  30% {
		clip: rect(31px, 9999px, 94px, 0);
	}
  40% {
		clip: rect(88px, 9999px, 98px, 0);
	}
  50% {
		clip: rect(9px, 9999px, 98px, 0);
	}
  60% {
		clip: rect(37px, 9999px, 17px, 0);
	}
  70% {
		clip: rect(77px, 9999px, 34px, 0);
	}
  80% {
		clip: rect(55px, 9999px, 49px, 0);
	}
  90% {
		clip: rect(10px, 9999px, 2px, 0);
	}
  to {
		clip: rect(35px, 9999px, 53px, 0);
	}
}

@keyframes scan {
	0%,
	20%,
	to {
		height: 0;
		transform: translate(-50%, 0.44em);
	}
	10%,
	15% {
		height: 1em;
		line-height: 0.2em;
		transform: translate(-55%, 0.09em);
	}
}

h1::after {
	content: attr(data-t);
	position: absolute;
	top: -8px;
	left: 50%;
	transform: translate(-50%, 0.34em);
	height: 0.5em;
	line-height: 0.1em;
	width: 100%;
	animation: scan 665ms ease-in-out 0.59 infinite alternate, glitch-anim 0.3s ease-in-out infinite alternate;
	overflow: hidden;
	opacity: 0.8;
}
</style>
