<template>
	<div>
		<a-row class="login-page">
			<!-- <img src="https://img10.360buyimg.com/imgzone/jfs/t1/141980/24/36079/94876/649abddfFfb50ea14/e976a4057bfd2abf.jpg" alt="" /> -->
			<div class="login-card">
				<span class="login-title">修改密码</span>
				<a-form layout="vertical" ref="loginFormRef" style="width: 357px">
					<a-input
						type="password"
						@input="onInput1"
						placeholder="请输入旧密码"
						style="border-radius: 5px; height: 58px; margin: 20px 0"
						size="large"
						v-model:value="oldPW"
						autocomplete="off"
					/>
					<a-input
						type="password"
						@input="onInput2"
						placeholder="请输入新密码"
						style="border-radius: 5px; height: 58px; margin: 20px 0"
						size="large"
						v-model:value="newPW"
						autocomplete="off"
					/>
					<a-input
						type="password"
						@input="onInput3"
						placeholder="请确认新密码"
						style="border-radius: 5px; height: 58px; margin: 20px 0"
						size="large"
						v-model:value="confirmNPW"
						autocomplete="off"
					/>
					<a-button class="upBtn" type="primary" block @click="handleSubmitClick">提交</a-button>
				</a-form>
			</div>
		</a-row>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, toRaw } from 'vue';
import { useRouter } from 'vue-router';
import LoginApi from '/@/utils/request/api/loginApi';
import { Modal, message } from 'ant-design-vue';
import { useUserStore } from "/@/store/modules/user";
import { ssoLogOut } from '/@/utils/request/api/SKII/mainApi';

const router = useRouter();
const useUserStoreObj = useUserStore()

const oldPW = ref('');
const newPW = ref('');
const confirmNPW = ref('');

const permissionKeyList: string[] = [];

const handleSubmitClick = async () => {
	if (!oldPW.value || !newPW.value || !confirmNPW.value) {
		message.error('请输入全部信息');
		return;
	}
	if (newPW.value !== confirmNPW.value) {
		message.error('两次密码不一致');
		return;
	}
	if (newPW.value == oldPW.value) {
		message.error('新密码与旧密码不能相同');
		return;
	}
	await LoginApi.modifyPW({ pwd: oldPW.value, newPwd: newPW.value });
	Modal.confirm({
		title: '提示',
		content: '密码修改成功，请重新登录',
		okText: '重新登录',
		cancelButtonProps: { style: { display: 'none' } },
		cancelText: '取消',
		onOk() {
			if (useUserStoreObj.userInfo.username == 'SKIIAdmin') {
				ssoLogOut()
			}
			router.push({ path: '/login', replace: true });
			useUserStoreObj.clearToken()
			useUserStoreObj.clearUserInfo()
		},
	});
};

const checkPermissionKeyList = (list: any[]) => {
	list.map((item) => {
		permissionKeyList.push(item.permissionKey);
		if (item.children && item.children.length > 0) {
			checkPermissionKeyList(item.children);
		}
	});
};
const onInput1 = (e) => {
	oldPW.value = e.target.value.replace(/\s/g, '');
};

const onInput2 = (e) => {
	newPW.value = e.target.value.replace(/\s/g, '');
};
const onInput3 = (e) => {
	confirmNPW.value = e.target.value.replace(/\s/g, '');
};
</script>

<style lang="less">
.login-page {
	display: flex;
	flex-direction: row;
	width: 100vw;
	height: 100vh;
	align-items: center;
	justify-content: flex-end;
	background-repeat: no-repeat;
	background-size: auto 100%;
	background-color: #28272c;
	background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/245958/6/3278/84358/65a8a069F15d78328/9ba6a7dea8f34dd0.png);

	img {
		width: 800px;
		height: 720px;
	}
}
.login-card {
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 520px;
	width: 480px;
	justify-content: center;
	background-image: -webkit-linear-gradient(top left, #243a7b, transparent, transparent, transparent);
	border-radius: 15px;
	color: #fff;
	margin-right: 10%;
}

.login-title {
	font-size: 21px;
	font-weight: bold;
	text-align: center;
	margin-bottom: 70px;
}

.login-page .ant-form-item-label>label {
	color: #fff !important;
}

.login-page .ant-form-item-label>label.ant-form-item-required:not(.ant-form-item-required-mark-optional):before {
	display: none;
}

.login-page .ant-input {
	height: 48px;
	background-color: transparent !important;
	border-top: none;
	border-right: none;
	border-left: none;
	border-image: initial;
	border-radius: 0 !important;
	border-bottom: 1px solid rgb(217, 217, 217);
	color: rgb(255, 255, 255);
}

.login-page .upBtn {
	background-image: -webkit-linear-gradient(top right, #253250, #243d66);
	margin-top: 30px;
	height: 50px;
	border-radius: 5px;
}
</style>
