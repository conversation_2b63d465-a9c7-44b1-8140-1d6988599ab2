<template>
    <div class="SKII-main">
        <div class="filter-container">
            <div class="filter-container-item">
                上传时间
                <a-range-picker v-model:value="value4" style="width: 220px;" format="YYYY-MM-DD" @change="changeDate"
                    :allowClear="false" />
            </div>
            <div class="filter-container-item">
                用户ID
                <a-input v-model:value="listQuery.openId" placeholder="请输入用户ID" allowClear style="width: 180px;" />
            </div>
            <div class="filter-container-item">
                选择状态
                <a-select v-model:value="listQuery.giftStatus" style="width: 180px;" allowClear :options="statusList"
                    placeholder="请选择状态"></a-select>
            </div>
            <div class="filter-container-item">
                奖品名称
                <a-input v-model:value="listQuery.giftTitle" placeholder="请输入奖品名称" allowClear style="width: 180px;" />
            </div>
            <a-button type="primary" @click="getNewList">筛选</a-button>
            <a-button type="primary" @click="exploreList">导出</a-button>
        </div>
        <div>
            批量修改状态
            <a-select v-model:value="editStatus" allowClear :options="statusList?.slice(1, 3)"
                placeholder="请选择修改状态"></a-select>
            <a-button type="primary" @click="allUpdate" style="margin-left: 20px;">修改</a-button>
        </div>
        <a-table :loading="tableLoading" :data-source="list" row-key="id" :columns="columns" bordered
            style="margin-top: 10px;" :pagination="false" :row-selection="rowSelection" :scroll="{ x: 2000 }">
            <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'memberStatus'">
                    <span>{{ text == 0?'默认':text == 1 ? '新客' : '老客' }}</span>
                </template>
                <template v-if="column.dataIndex === 'giftStatus'">
                    <span>{{ checkStatus(text) }}</span>
                </template>
                <template v-if="column.dataIndex === 'picUrl'">
                    <div class="img-box">
                        <a-image v-for="(item, index) in text" :key="index" :src="item" :width="30" :height="30" />
                    </div>
                </template>
                <template v-if="column.dataIndex === 'operate'">
                    <a-button :disabled="record.giftStatus != 0" type="primary"
                        @click="showDialog(1, record.id)">中奖</a-button>
                    <a-button :disabled="record.giftStatus != 0" type="primary"
                        @click="buttonUpdate(2, record.id)">未中奖</a-button>
                </template>
                <template v-if="column.dataIndex === 'expressNumber'">
                    <span style="margin-right: 10px;">{{ text }}</span>
                    <a-button :hidden="!!text" type="primary" :disabled="record.giftStatus != 1"
                        @click="addExpressNumber(record.id)">填写物流单号</a-button>
                </template>
            </template>
        </a-table>
        <LzPagination v-show="total > 0" :total="total" v-model:page="listQuery.pageNo"
            v-model:pageSize="listQuery.pageSize" @change="getList" />

        <a-modal v-model:visible="dialogVisible" title="填写物流单号" @ok="handleOk" @cancel="clearInfo">
            <a-input v-model:value="expressInfo.expressNumber" :maxlength="35" @keydown="handleKeyDown" show-count
                placeholder="请输入物流单号" />
        </a-modal>
        <a-modal v-model:visible="choosePrize" title="选择奖品" @ok="handleOk2" @cancel="clearInfo2">
            <a-select v-model:value="changeUserInfo.giftId" :options="prizeList" placeholder="请选择奖品"></a-select>
        </a-modal>
    </div>
</template>
<script setup lang="ts">
import type { Dayjs } from 'dayjs'
import moment from 'moment';
import { Ref, onMounted, reactive, unref, ref, computed } from "vue";
import { type TableProps, type TableColumnType, message, SelectProps, Modal } from 'ant-design-vue';
import { ElMessageBox, dayjs } from 'element-plus';
import { downloadBlob } from '/@/utils/request/api/commonFun'
import { getRecordList, updateGiftStatus, updateExpressNumber, exportRecord, getPrizeList } from '/@/utils/request/api/SKII/mainApi'

const dialogVisible = ref(false)
//填写物流单号
const expressInfo = ref({
    expressNumber: '',
    recordId: null
})
// 选择奖品弹窗
const choosePrize = ref(false)

const prizeList = ref([])
//查询条件
interface ListQuery {
    startTime: string;
    endTime: string;
    openId: number | string;
    giftStatus: any;
    giftTitle: number | string,
    pageNo: number;
    pageSize: number;
}
// 禁止输入空格
const handleKeyDown = (event: any) => {
    if (event.keyCode === 32) {
        event.preventDefault();
    }
}
const listQuery: Ref<ListQuery> = ref({
    startTime: moment(new Date().setTime(new Date().getTime() - 3600 * 1000 * 24 * 30)).format('YYYY-MM-DD'),
    endTime: moment(new Date().getTime()).format('YYYY-MM-DD'),
    openId: '',
    giftStatus: null,
    giftTitle: null,
    pageNo: 1,
    pageSize: 20,
});
//表格信息
interface DataType {
    id: string,
    createTime: string,
    openId: string,
    orderId: string,
    orderAmount: string,
    orderSku: string,
    memberStatus: string,
    picUrl: any,
    receiver: string,
    phoneNumber: string,
    province: string,
    city: string,
    region: string,
    detailAddress: string,
    giftStatus: string,
    expressNumber: string,
}
let list = reactive([])

const total = ref(0)
// 中奖状态列表
const statusList = ref<SelectProps['options']>([
    {
        value: 0,
        label: '等待开奖'
    },
    {
        value: 1,
        label: '中奖'
    },
    {
        value: 2,
        label: '未中奖'
    },
])
// 回显中奖状态文字
const checkStatus = (status: number | string) => {
    let text;
    statusList.value.forEach(item => {
        if (item.value == status) {
            text = item.label
        }
    })
    return text
}

const columns: TableColumnType<DataType>[] = [
    {
        title: '奖品名称',
        dataIndex: 'giftTitle',
        align: 'center',
        width: 150,
    },
    {
        title: '上传时间',
        dataIndex: 'createTime',
        align: 'center',
        width: 200,
    },
    {
        title: '用户ID',
        dataIndex: 'openId',
        align: 'center',
        width: 150,
    },
    {
        title: '订单号',
        dataIndex: 'orderId',
        align: 'center',
        width: 150,
    },
    {
        title: '订单金额',
        dataIndex: 'orderAmount',
        align: 'center',
        width: 150,
    },
    {
        title: 'SKU',
        dataIndex: 'orderSku',
        align: 'center',
        width: 150,
    },
    {
        title: '新老客',
        dataIndex: 'memberStatus',
        align: 'center',
        width: 150,
    },
    {
        title: '上传图片',
        dataIndex: 'picUrl',
        align: 'center',
        width: 150,
    },
    {
        title: '收货姓名',
        dataIndex: 'receiver',
        align: 'center',
        width: 150,
    },
    {
        title: '收货电话',
        dataIndex: 'phoneNumber',
        align: 'center',
        width: 150,
    },
    {
        title: '省',
        dataIndex: 'province',
        align: 'center',
        width: 150,
    },
    {
        title: '市',
        dataIndex: 'city',
        align: 'center',
        width: 150,
    },
    {
        title: '区',
        dataIndex: 'region',
        align: 'center',
        width: 150,
    },
    {
        title: '地址详情',
        dataIndex: 'detailAddress',
        align: 'center',
        width: 150,
    },
    {
        title: '状态',
        dataIndex: 'giftStatus',
        align: 'center',
        width: 150,
    },
    {
        title: '中奖操作',
        dataIndex: 'operate',
        align: 'center',
        width: 200,
        fixed: 'right',
    },
    {
        title: '发货物流操作',
        dataIndex: 'expressNumber',
        align: 'center',
        width: 200,
        fixed: 'right',
    },
]

const tableLoading = ref(false)

interface UserQuery {
    recordId: any;
    recordIdList: any,
    giftStatus: any;
    giftId: any
}
// 修改状态接口参数
const changeUserInfo: Ref<UserQuery> = ref({
    giftStatus: null,
    recordIdList: [],
    recordId: null,
    giftId: null,
})

const selectedRowKeys = ref<DataType['id'][]>([]);
// 获取表格选中的数据
const rowSelection = computed(() => {
    return {
        onChange: (selectedKeys: string[], selectedRows: DataType[]) => {
            selectedRowKeys.value = selectedKeys;
            console.log(`selectedRowKeys: ${selectedKeys}`, 'selectedRows: ', selectedRows);
        },
        // 非等待开奖状态禁止选择
        getCheckboxProps: (record: DataType) => ({
            disabled: record.giftStatus != 0,
        }),
        selectedRowKeys: unref(selectedRowKeys),
        fixed: 'left'
    }
});

// 批量修改状态字段
const editStatus = ref()

// 修改中奖状态接口
const batchUpdateStatus = async () => {
    console.log(changeUserInfo.value, 'changeUserInfo');
    // return
    const res = await updateGiftStatus(changeUserInfo.value)
    if (res) {
        getList()
        Object.keys(changeUserInfo.value).forEach((key) => {
            changeUserInfo.value[key] = null
        })
        selectedRowKeys.value = []
        editStatus.value = null
        message.success('中奖状态修改成功')
        choosePrize.value = false
        clearUserInfo()
    }
}

// 批量修改状态
const allUpdate = () => {
    changeUserInfo.value.recordId = null
    changeUserInfo.value.giftStatus = editStatus.value
    changeUserInfo.value.recordIdList = selectedRowKeys.value
    if (!changeUserInfo.value.recordIdList || changeUserInfo.value.recordIdList.length < 1) {
        message.error('请选择修改数据')
        return
    }
    if (!changeUserInfo.value.giftStatus) {
        message.error('请选择修改状态')
        return
    }
    if (changeUserInfo.value.giftStatus == 1) {
        choosePrize.value = true
    } else {
        Modal.confirm({
            title: '提醒',
            content: '确认修改所选数据的中奖状态?',
            okText: '确定',
            cancelText: '取消',
            onOk() {
                batchUpdateStatus()
            },
        })
    }

}
// 中奖按钮展示选择奖品弹窗 并赋值
const showDialog = (type: number, id: number | string) => {
    choosePrize.value = true
    changeUserInfo.value.giftStatus = type
    changeUserInfo.value.recordId = id
    changeUserInfo.value.recordIdList = null
}
// 清空奖品id
const clearInfo2 = () => {
    changeUserInfo.value.giftId = null
}

const handleOk2 = () => {
    if (changeUserInfo.value.giftId == null) {
        message.error("请选择奖品")
        return
    }
    Modal.confirm({
        title: '提醒',
        content: '确认修改当前中奖状态?',
        okText: '确定',
        cancelText: '取消',
        onOk() {
            batchUpdateStatus()
        },
    })
}

//未中奖修改
const buttonUpdate = (type: number, id: number | string) => {
    changeUserInfo.value.recordIdList = null
    changeUserInfo.value.giftStatus = type
    changeUserInfo.value.recordId = id
    Modal.confirm({
        title: '提醒',
        content: '确认修改当前中奖状态?',
        okText: '确定',
        cancelText: '取消',
        onOk() {
            batchUpdateStatus()
        },
        onCancel() {
            clearUserInfo()
        }
    })
}

const clearUserInfo = () => {
    Object.keys(changeUserInfo.value).forEach(key => {
        changeUserInfo.value[key] = null
    })
}

type RangeValue = [Dayjs, Dayjs];
// 默认时间
const value4 = ref<RangeValue>([
    dayjs(new Date().getTime() - 3600 * 1000 * 24 * 30),
    dayjs(new Date().getTime())
])

// 选择时间
const changeDate = (date: dayjs, dateString: [string, string]) => {
    listQuery.value.startTime = dateString[0]
    listQuery.value.endTime = dateString[1]
}
const getNewList=()=>{
    listQuery.value.pageNo=1
    getList()
}
// 获取数据列表
const getList = async () => {
    tableLoading.value = true
    setTimeout(() => {
        tableLoading.value = false
    }, 1000)
    console.log('获取数据列表', listQuery.value)
    const res = await getRecordList(listQuery.value)
    if (res) {
        res.records.forEach((item: any) => {
            if (item.picUrl?.toString().indexOf('[') != -1) {
                item.picUrl = JSON.parse(item.picUrl)
            } else {
                item.picUrl = item.picUrl?.split(',')
            }
        })
        list = res.records
        listQuery.value.pageNo = res.current
        listQuery.value.pageSize = res.size
        total.value = res.total
    }

}

// 获取奖品列表
const getPrizesList = async () => {
    const res = await getPrizeList({ activityId: "skii20240301" })
    if (res) {
        res.forEach(item => {
            prizeList.value.push({
                value: item.id,
                label: item.giftTitle
            })
        })
    }
}

// 导出
const exploreList = async () => {
    const res: any = await exportRecord(listQuery.value)
    downloadBlob(res.data, res, 'SKII活动数据.xlsx')
}
// 显示填写快递单号弹窗并赋值id
const addExpressNumber = (id: number | string) => {
    dialogVisible.value = true
    expressInfo.value.recordId = id
}
// 清空快递信息
const clearInfo = () => {
    Object.keys(expressInfo.value).forEach(key => {
        expressInfo.value[key] = null
    })
}
// 确认提交快递单号
const handleOk = () => {
    if (!expressInfo.value.expressNumber) {
        return message.warning('请输入快递单号')
    }
    ElMessageBox.confirm(`您输入的快递单号是${expressInfo.value.expressNumber},是否确认提交？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
            const res = await updateExpressNumber(expressInfo.value)
            if (res) {
                message.success('提交成功')
                dialogVisible.value = false
                getList()
            }
            clearInfo()
        })
}

onMounted(() => {
    getList()
    getPrizesList()
})
</script>
<style lang="less" scoped>
:deep(.ant-image){
    margin-right: 5px;
}
:deep(.ant-image-img){
    width: 100%;
    height: auto
}
.SKII-main {
    padding: 0 20px;
}
.img-box{
    white-space: nowrap;
    height: 85px;
    overflow-x: auto;
}
.filter-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .filter-container-item {
        margin-right: 20px;
    }
}
</style>