<template>
	<div>
		<a-modal :title="title" v-model:visible="visible" cancelText="取消" okText="提交" @ok="handleOk">
			<a-form :model="permData" ref="dataFormRef" v-if="modalType == 'PERM'" :label-col="{ style: { width: '150px' } }" :wrapper-col="{ span: 14 }">
				<a-form-item label="权限名" name="name" :rules="[{ required: true, message: '请输入权限名' }]">
					<a-input v-model:value="permData.name" autocomplete="off" />
				</a-form-item>
				<a-form-item label="权限描述" name="description">
					<a-input v-model:value="permData.description" autocomplete="off" />
				</a-form-item>
				<a-form-item label="权限类型" name="type">
					<a-select v-model:value="permData.type" placeholder="请选择权限类型">
						<a-select-option :value="item.value" v-for="item in typeList">{{ item.txt }}</a-select-option>
					</a-select>
				</a-form-item>
				<a-form-item label="路由地址" name="permissionKey">
					<a-input v-model:value="permData.permissionKey" autocomplete="off" />
				</a-form-item>
			</a-form>
			<a-form :model="roleData" ref="dataFormRef" v-if="modalType == 'ROLE'" :label-col="{ style: { width: '150px' } }" :wrapper-col="{ span: 14 }">
				<a-form-item label="角色名称" name="name" :rules="[{ required: true, message: '请输入权限名' }]">
					<a-input v-model:value="roleData.name" autocomplete="off" />
				</a-form-item>
				<a-form-item label="角色Key" name="roleKey">
					<a-input v-model:value="roleData.roleKey" autocomplete="off" />
				</a-form-item>
				<a-form-item label="描述" name="description">
					<a-input v-model:value="roleData.description" autocomplete="off" />
				</a-form-item>
			</a-form>
			<a-form :model="userData" ref="dataFormRef" v-if="modalType == 'USER'" :label-col="{ style: { width: '150px' } }" :wrapper-col="{ span: 14 }">
				<a-form-item label="用户名" name="username" :rules="[{ required: true, message: '请输入用户名' }]">
					<a-input v-model:value="userData.username" autocomplete="off" />
				</a-form-item>
				<a-form-item label="密码" name="password" :rules="[{ required: true, message: '请输入密码' }]" v-if="ModalOpenTypeKey === ModalOpenType.ADD">
					<a-input v-model:value="userData.password" autocomplete="off" />
				</a-form-item>
				<a-form-item label="状态" name="status">
					<a-select v-model:value="userData.status" placeholder="请选用户状态">
						<a-select-option :value="item.value" v-for="item in statusList">{{ item.txt }}</a-select-option>
					</a-select>
				</a-form-item>
				<a-form-item label="角色" name="status">
					<a-select v-model:value="selectId" :options="allRoleList" mode="multiple" size="large" placeholder="请选择角色"></a-select>
				</a-form-item>
			</a-form>
		</a-modal>
	</div>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick, computed, Ref } from 'vue';
import { permConfig, roleConfig, userConfig } from '../permConfig';
import { addPermission, addRole, editRole, saveUserInfo, getUserInfo } from '/@/utils/request/api/authApi';
const title: Ref<string> = ref('');
const emit = defineEmits(['refreshPermData', 'refreshRoleData', 'refreshUserData']);
const visible = ref(false);
const props = defineProps([]);

const ModalOpenType = {
	ADD: 0,
	EDIT: 1
};

const ModalOpenTypeKey = ref<number | null>(null);

const ModalOpenDeatil = {
	USER: '用户',
	ROLE: '角色',
	PERM: '权限'
};
const modalType = ref('');

const typeList = [
	{ value: '0', txt: '菜单权限' },
	{ value: '1', txt: '按钮权限' }
];

const statusList = [
	{ value: '0', txt: '启用' },
	{ value: '1', txt: '禁用' }
];

const permData = ref<permConfig>({
	id: null,
	parentId: null,
	name: '', //页面标题
	description: '',
	permissionKey: '',
	route: '',
	icon: '',
	type: '0',
});

const roleData = ref<roleConfig>({
	id: null,
	name: '',
	description: '',
	roleKey: '',
	perms: []
});

const userData = ref<userConfig>({
	id: null,
	username: '',
	password: '',
	status: '0',
	usertype: 0
});

const allRoleList = ref<any>([]);
const selectId = ref<any>([]);

const open = (type, detail, item, list) => {
	visible.value = true;
	nextTick(async () => {
		modalType.value = detail;
		ModalOpenTypeKey.value = type;
		if (type === ModalOpenType.EDIT) {
			title.value = `编辑${ModalOpenDeatil[detail]}`;
			switch (detail) {
				case 'PERM':
					permData.value = JSON.parse(JSON.stringify(item));
					break;
				case 'ROLE':
					roleData.value = JSON.parse(JSON.stringify(item));
					break;
				case 'USER':
					allRoleList.value = JSON.parse(JSON.stringify(list));
					allRoleList.value.map((item) => {
						item.value = item.id;
						item.label = item.name;
					});
					getUserInfoRequest(item.id);
					break;
				default:
					break;
			}
			// data.value = item;
		} else {
			title.value = `创建${ModalOpenDeatil[detail]}`;
			if (detail === 'PERM') {
				permData.value = {
					id: null,
					parentId: null,
					name: '', //页面标题
					description: '',
					permissionKey: '',
					route: '',
					icon: '',
					type: 0
				};
				permData.value.parentId = item;
			} else if (detail === 'ROLE') {
				roleData.value = {
					id: null,
					name: '',
					description: '',
					roleKey: ''
				};
			} else if (detail === 'USER') {
				userData.value = {
					id: null,
					username: '',
					password: '',
					realname: '',
					slat: '',
					sex: null,
					phone: '',
					email: '',
					usertype: 0,
					status: '0'
				};
				selectId.value = [];
				allRoleList.value = JSON.parse(JSON.stringify(list));
				allRoleList.value.map((item) => {
					item.value = item.id;
					item.label = item.name;
				});
			}
		}
	});
};

defineExpose({ open });

const dataFormRef = ref();
const handleOk = () => {
	dataFormRef.value.validate().then(async () => {
		//创建权限接口
		console.log(modalType.value);
		if (modalType.value === 'PERM') {
			addPermissionRequest();
		} else if (modalType.value === 'ROLE') {
			if (ModalOpenTypeKey.value === ModalOpenType.EDIT) {
				editRoleRequest();
			} else {
				addRoleRequest();
			}
		} else if (modalType.value === 'USER') {
			saveUserInfoRequest();
		}
	});
};

const addPermissionRequest = async () => {
	await addPermission(permData.value);
	emit('refreshPermData');
	visible.value = false;
};

const addRoleRequest = async () => {
	await addRole(roleData.value);
	emit('refreshRoleData');
	visible.value = false;
};

const editRoleRequest = async () => {
	const { id, name, roleKey, description } = roleData.value;
	await editRole({ id, name, roleKey, description });
	emit('refreshRoleData');
	visible.value = false;
};

const saveUserInfoRequest = async () => {
	let data = userData.value as any;
	data.roleIdList = selectId;
	await saveUserInfo(data);
	emit('refreshUserData');
	visible.value = false;
};

const getUserInfoRequest = async (id) => {
	const res = (await getUserInfo({ id })) as any;
	userData.value = { ...userData.value, username: res.username, password: res.password, id: res.id, status: res.status, usertype: res.usertype };
	selectId.value = res.roleEntityList.map((item) => {
		return item.id;
	});
};
</script>

<style lang="less" scoped></style>
