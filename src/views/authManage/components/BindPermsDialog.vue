<template>
	<div>
		<a-modal :title="title" v-model:visible="visible" cancelText="取消" okText="提交" @ok="handleOk">
			<a-tree :tree-data="allPerm" checkable v-model:checkedKeys="checkedKeys" :selectable="false" v-if="allPerm.length > 0">
				<!-- <template #title="{ name, key }"> -->
				<!-- <span v-if="key === '0-0-1-0'" style="color: #1890ff">{{ title }}</span> -->
				<!-- <template>{{ name }}</template>
						</template> -->
			</a-tree>
		</a-modal>
	</div>
</template>

<script setup lang="ts">
import { ref, Ref, nextTick } from 'vue';
import { queryRolePermission, roleBindPermission, getUserInfo, userBindPermission } from '/@/utils/request/api/authApi';
import { message } from 'ant-design-vue';
const title: Ref<string> = ref('绑定权限');
const visible = ref(false);
const checkedKeys = ref<string[]>([]);
const props = defineProps([]);

const modalType = ref('');

const roleId = ref('');
const userId = ref('');

const allPerm = ref([]);

const open = (detail, allPermData, id) => {
	modalType.value = detail;
	nextTick(async () => {
		allPerm.value = JSON.parse(JSON.stringify(allPermData.value));
		if (detail === 'USER') {
			userId.value = id;
			getUserInfoRequest();
		} else if (detail === 'ROLE') {
			roleId.value = id;
			queryRolePermissionRequest();
		}
		visible.value = true;
		checkedKeys.value = [];
	});
};

const queryRolePermissionRequest = async () => {
	const res = (await queryRolePermission({ roleId: roleId.value })) as any[];
	const list = res.map((item) => {
		return item.id;
	});
	createCheckKey(allPerm.value, list);
};

const roleBindPermissionRequest = async (list) => {
	await roleBindPermission({ roleId: roleId.value, permissionIdList: list });
	message.success('绑定成功');
	visible.value = false;
};

defineExpose({ open });

const handleOk = () => {
	const list = checkedKeys.value;
	let result = [];
	list.map((item) => {
		const p = item.split('-');
		let data = JSON.parse(JSON.stringify(allPerm.value));
		p.map((a) => {
			// data = data[a].children;
			if (result.indexOf(data[a].id) == -1) {
				result.push(data[a].id);
			}
			if (data[a].children) {
				data = data[a].children;
			}
		});
	});
	if (modalType.value === 'USER') {
		userBindPermissionRequest(result);
	} else {
		roleBindPermissionRequest(result);
	}
};

const createCheckKey = (permList, keysList) => {
	// console.log(permList, keysList);
	// return;
	permList.map((item, index) => {
		if ((!item.children || item.children.length < 1) && keysList.indexOf(item.id) != -1) {
			checkedKeys.value.push(item.key);
		} else if (item.children && item.children.length > 0 && keysList.indexOf(item.id) != -1) {
			let selectAll = true;
			item.children.map((itm) => {
				if (keysList.indexOf(itm.id) == -1) {
					selectAll = false;
				}
			});
			if (selectAll) {
				checkedKeys.value.push(item.key);
			}
		}
		createCheckKey(item.children, keysList);
	});
};

const getUserInfoRequest = async () => {
	const res = (await getUserInfo({ id: userId.value })) as any;
	const list = [];
	checkPermId(res.permissionList, list);
	console.log(list);
	createCheckKey(allPerm.value, list);
};

const checkPermId = (list, targetList) => {
	list.map((item) => {
		targetList.push(item.id);
		if (item.children && item.children.length > 0) {
			checkPermId(item.children, targetList);
		}
	});
};

const userBindPermissionRequest = async (list) => {
	await userBindPermission({ userId: userId.value, bindPermissionId: list });
	message.success('绑定成功');
	visible.value = false;
};
</script>

<style lang="less" scoped></style>
