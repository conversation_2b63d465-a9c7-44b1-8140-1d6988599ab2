<template>
	<div class="zst-main">
		<a-form :model="decoData" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
			<a-form-item label="活动ID">
				<a-input :value="activityId" class="width150" disabled />
			</a-form-item>
			<a-form-item label="kv图" name="homeBg">
				<MyUpload @handleAvatarSuccess="handleHomeBg" :imageUrl="decoData.homeBg" :size="1" class="hide-input">
					<template #remark>
						<div>图片尺寸建议 宽750</div>
					</template>
				</MyUpload>
			</a-form-item>
			<a-form-item label="信息收集页" name="infoBg">
				<MyUpload @handleAvatarSuccess="handleInfoBg" :imageUrl="decoData.infoBg" :size="1" class="hide-input">
					<template #remark>
						<div>图片尺寸建议 宽750</div>
					</template>
				</MyUpload>
			</a-form-item>
		</a-form>
		<a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
			<a-form-item label="优惠券">
				<a-table :columns="couponColumns" :data-source="formData.couponList" :pagination="false" bordered>
					<template #bodyCell="{ record, index, column }">
						<template v-if="column.key === 'couponId'">
							<a-form-item :name="['couponList', index, 'couponId']" :rules="{ required: true, message: '请输入优惠券id', trigger: 'change' }">
								<a-input v-model:value="formData.couponList[index].couponId" :disabled="!!formData.couponList[index].id" />
							</a-form-item>
						</template>
						<template v-if="column.key === 'couponTotalNum'">
							<a-form-item :name="['couponList', index, 'couponTotalNum']" :rules="{ required: true, message: '请输入奖品总数量', trigger: 'change' }">
								<a-input-number class="width150" v-model:value="formData.couponList[index].couponTotalNum" :min="1" />
							</a-form-item>
						</template>

						<template v-if="column.key === 'action'">
							<a-button type="link" @click="deleteCoupon(index)">删除</a-button>
						</template>
					</template>
				</a-table>
				<a-button class="add-coupon" type="primary" @click="() => formData.couponList.push(JSON.parse(JSON.stringify(couponDefault)))" :disabled="!!formData.couponList.length"
					>新增优惠券</a-button
				>
			</a-form-item>
			<a-form-item label="商品列表">
				<a-table :columns="goodsColumns" :data-source="formData.skuInfoList" :pagination="false" bordered>
					<template #bodyCell="{ record, index, column }">
						<template v-if="column.key === 'skuId'">
							<a-form-item :name="['skuInfoList', index, 'skuId']" :rules="{ required: true, message: '请输入商品ID', trigger: 'change' }">
								<a-input v-model:value="formData.skuInfoList[index].skuId" />
							</a-form-item>
						</template>
						<template v-if="column.key === 'skuName'">
							<a-form-item :name="['skuInfoList', index, 'skuName']" :rules="{ required: true, message: '请输入商品名称', trigger: 'change' }">
								<a-input v-model:value="formData.skuInfoList[index].skuName" />
							</a-form-item>
						</template>
						<template v-if="column.key === 'skuTypeName'">
							<a-form-item :name="['skuInfoList', index, 'skuTypeName']" :rules="{ required: true, message: '请选择商品分类名称', trigger: 'change' }">
								<a-select
									v-model:value="formData.skuInfoList[index].skuTypeName"
									@change="
										(value: any) => {
											changeSkuTypeName(value, index);
										}
									"
									:options="skuTypeNameOptions"
									class="width150"
								></a-select>
							</a-form-item>
						</template>
						<template v-if="column.key === 'skuPhase'">
							<a-form-item :name="['skuInfoList', index, 'skuPhase']" :rules="{ required: true, message: '请选择商品段位', trigger: 'change' }">
								<a-select v-model:value="formData.skuInfoList[index].skuPhase" :options="skuPhaseOptions" class="width150"></a-select>
							</a-form-item>
						</template>
						<template v-if="column.key === 'skuImg'">
							<a-form-item :name="['skuInfoList', index, 'skuImg']" :rules="{ required: true, message: '请上传奖品图', trigger: 'change' }">
								<MyUpload
									@handleAvatarSuccess="
										(data) => {
											formData.skuInfoList[index].skuImg = data.res.data;
											formRef.validate([['skuInfoList', index, 'skuImg']]);
										}
									"
									:imageUrl="formData.skuInfoList[index].skuImg"
									:size="1"
									class="hide-input"
								>
									<template #remark>
										<div>图片尺寸建议 宽750</div>
									</template>
								</MyUpload>
							</a-form-item>
						</template>
						<template v-if="column.key === 'action'">
							<a-button class="table-btn" type="link" @click="deleteSku(index)">删除</a-button>
							<a-button class="table-btn" type="link" @click="upSku(index)" v-if="index !== 0">上移</a-button>
							<a-button class="table-btn" type="link" @click="downSku(index)" v-if="index !== formData.skuInfoList.length - 1">下移</a-button>
						</template>
					</template>
				</a-table>
				<a-button class="add-goods" type="primary" @click="() => formData.skuInfoList.push(JSON.parse(JSON.stringify(skuDefault)))">新增商品</a-button>
				<!-- <a-button class="add-goods" type="primary" @click="addSku">快速增加商品</a-button> -->
			</a-form-item>
			<a-form-item label=" " :colon="false">
				<a-button type="primary" @click="confirmForm">保存活动</a-button>
			</a-form-item>
		</a-form>

		<a-modal :visible="visible" title="确认" :confirm-loading="confirmLoading" @ok="createAct" @cancel="visible = false">
			<p>是否确认保存活动？</p>
		</a-modal>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import { createOrUpdateActivity, getActivityConfigInfo, getSkuTypeNameList } from '/@/utils/request/api/FH/mainApi';
import { useUserStore } from '/@/store/modules/user';
import { getActivityId } from '../250115/activeInfo';
import { message } from 'ant-design-vue';

const useUserStoreObj = useUserStore();
const formRef = ref();
const visible = ref(false);
const confirmLoading = ref(false);
const activityId = getActivityId(useUserStoreObj.userInfo.username) ?? 'fh14';
const decoData = ref({
	homeBg: '',
	infoBg: ''
});

const handleHomeBg = (data: any) => {
	decoData.value.homeBg = data.res.data;
};
const handleInfoBg = (data: any) => {
	decoData.value.infoBg = data.res.data;
};

interface Coupon {
	id?: number;
	couponId: string;
	couponTotalNum: string;
}

interface Sku {
	skuId: string;
	skuName: string;
	skuTypeName: string;
	skuPhase: string | number;
	skuImg: string;
}

interface FormData {
	couponList: Coupon[];
	skuInfoList: Sku[];
}

const couponDefault: Coupon = {
	couponId: '',
	couponTotalNum: ''
};

const skuDefault: Sku = {
	skuId: '',
	skuName: '',
	skuTypeName: '',
	skuPhase: '',
	skuImg: ''
};

const formData = ref<FormData>({
	couponList: [],
	skuInfoList: []
});
const rules: Record<string, Rule[]> = {
	// name: [{ required: true, message: '请输入活动名称', trigger: 'change' }],
	// rangeDate: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
	// otherPictureName: [{ required: true, message: '请输入其他照片名称', trigger: 'change' }],
	// rule: [{ required: true, message: '请输入活动规则', trigger: 'change' }]
};

const skuTypeNameOptions = ref([]);
const skuPhaseOptions = ref([
	{
		value: 0,
		label: '0-6个月'
	},
	{
		value: 1,
		label: '6-12个月'
	},
	{
		value: 2,
		label: '12-36个月'
	}
]);

const couponColumns = [
	{
		title: '优惠券id',
		dataIndex: 'couponId',
		key: 'couponId'
	},
	{
		title: '奖品总数量',
		dataIndex: 'couponTotalNum',
		key: 'couponTotalNum'
	},
	{
		title: '操作',
		key: 'action'
	}
];
const goodsColumns = [
	{
		title: '商品ID',
		dataIndex: 'skuId',
		key: 'skuId'
	},
	{
		title: '商品名称',
		dataIndex: 'skuName',
		key: 'skuName'
	},
	{
		title: '商品分类名称',
		dataIndex: 'skuTypeName',
		key: 'skuTypeName'
	},
	{
		title: '商品段位',
		dataIndex: 'skuPhase',
		key: 'skuPhase'
	},
	{
		title: '商品图片',
		dataIndex: 'skuImg',
		key: 'skuImg'
	},
	{
		title: '操作',
		key: 'action'
	}
];

const changeSkuTypeName = (skuTypeName: string, index: number) => {
	if (!formData.value.skuInfoList[index].skuName) {
		formData.value.skuInfoList[index].skuName = skuTypeName;
	}
	const skuPhaseText = skuTypeName.split('段')[0];
	const lastChar = skuPhaseText ? skuPhaseText.slice(-1) : '';

	// 判断最后一个字符是否是数字1,2,3
	if (['1', '2', '3'].includes(lastChar)) {
		formData.value.skuInfoList[index].skuPhase = +lastChar - 1;
	} else if (['一', '二', '三'].includes(lastChar)) {
		const skuPhase = ['一', '二', '三'].indexOf(lastChar);
		formData.value.skuInfoList[index].skuPhase = skuPhase;
	}
};
const deleteCoupon = (index: number) => {
	formData.value.couponList.splice(index, 1);
};
const deleteSku = (index: number) => {
	formData.value.skuInfoList.splice(index, 1);
};
// 上移sku
const upSku = (index: number) => {
	if (index > 0) {
		const temp = formData.value.skuInfoList[index];
		formData.value.skuInfoList.splice(index, 1);
		formData.value.skuInfoList.splice(index - 1, 0, temp);
	}
};
// 下移sku
const downSku = (index: number) => {
	if (index < formData.value.skuInfoList.length - 1) {
		const temp = formData.value.skuInfoList[index];
		formData.value.skuInfoList.splice(index, 1);
		formData.value.skuInfoList.splice(index + 1, 0, temp);
	}
};

const getActInfo = async () => {
	try {
		const data = (await getActivityConfigInfo({
			activityId
		})) as any;
		formData.value.couponList = data.couponInfo ? [data.couponInfo] : [];
		formData.value.skuInfoList = data.skuInfoList;
		decoData.value = JSON.parse(data.activityIntroduce);
	} catch (error) {
		console.error(error);
	}
};
const getSkuTypeNameOptions = async () => {
	try {
		const data = (await getSkuTypeNameList({})) as any;
		skuTypeNameOptions.value = data.map((item: any) => {
			return {
				value: item.skuTypeName,
				label: item.skuTypeName
			};
		});
	} catch (error) {
		console.error(error);
		message.error('获取商品类型失败');
	}
};

const confirmForm = () => {
	if (formData.value.couponList.length === 0) {
		message.error('请添加优惠券');
		return;
	}
	if (formData.value.skuInfoList.length === 0) {
		message.error('请添商品');
		return;
	}
	formRef.value.validate().then(() => {
		visible.value = true;
	});
};

const createAct = async () => {
	try {
		confirmLoading.value = true;
		const data = await createOrUpdateActivity({
			activityId,
			activityIntroduce: JSON.stringify(decoData.value),
			couponInfo: formData.value.couponList[0],
			skuInfoList: formData.value.skuInfoList
		});
		message.success('保存成功');
		visible.value = false;
		confirmLoading.value = false;
		getActInfo();
	} catch (error: any) {
		console.error(error);
		confirmLoading.value = false;
		message.error(error.response.data.data.message);
	}
};

const _skuList = [
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465305173935618',
		skuName: '卓睿1段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/ajmho42u2ewqvidxrq4jgw62acoh85gw.png',
		skuTypeName: '卓睿1段',
		skuPhase: 0
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465374915046914',
		skuName: '卓睿2段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/xvzrw2b9g2jtk9ubg9duodtrdqgzmzca.png',
		skuTypeName: '卓睿2段',
		skuPhase: 1
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465355177161986',
		skuName: '卓睿3段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/trhu2baog0jb43g051w4bnf5sfotanal.png',
		skuTypeName: '卓睿3段',
		skuPhase: 2
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465214300089346',
		skuName: '卓睿A2 1段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/pt9mumu2o3ee7vlekjk9jy8mw854guz4.png',
		skuTypeName: '卓睿A2 1段',
		skuPhase: 0
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465438906231810',
		skuName: '卓睿A2 2段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/k7rht7z1hxa95qfcsu5pfjtv3hxr6w5c.png',
		skuTypeName: '卓睿A2 2段',
		skuPhase: 1
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465438890844418',
		skuName: '卓睿A2 3段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/q3av1ynoe3losqavdeijhoqka9zfm20o.png',
		skuTypeName: '卓睿A2 3段',
		skuPhase: 2
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465394109528322',
		skuName: '星飞帆1段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/pzwic5hwh0p9dqdfo4jqtg9j4tk6v9hu.png',
		skuTypeName: '星飞帆1段',
		skuPhase: 0
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465394428904450',
		skuName: '星飞帆2段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/40c5h2mc7vtwzc3d9dth8odoqa5y4i5i.png',
		skuTypeName: '星飞帆2段',
		skuPhase: 1
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465249541245954',
		skuName: '星飞帆3段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/uewhtqybsg38i9cn4wy3mbzo35r8ruuz.png',
		skuTypeName: '星飞帆3段',
		skuPhase: 2
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465304799175682',
		skuName: '臻爱倍护1段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/gd9de6vp4oz5ttr4j9ooq3rica5ybujb.png',
		skuTypeName: '臻爱倍护1段',
		skuPhase: 0
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465228872301314',
		skuName: '臻爱倍护2段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/srq2u0obqqa4l9aromn29bhepdemf2m1.png',
		skuTypeName: '臻爱倍护2段',
		skuPhase: 1
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465374627303682',
		skuName: '臻爱倍护3段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/elrpyhuulifxp8h1adjd401tdj5thx5r.png',
		skuTypeName: '臻爱倍护3段',
		skuPhase: 2
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465510065592322',
		skuName: '星飞帆900g 1段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/fpkt7v8c2kokj6h76pcfjg7wlp4dhtto.png',
		skuTypeName: '星飞帆900g 1段',
		skuPhase: 0
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465491855205122',
		skuName: '星飞帆900g 2段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/k0ursvvui7h7a6h28ys6mu4wbwhlrod0.png',
		skuTypeName: '星飞帆900g 2段',
		skuPhase: 1
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465374982868738',
		skuName: '星飞帆900g 3段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/53ofmr5hsi96pqofpiouzgcvcf9ajc4p.png',
		skuTypeName: '星飞帆900g 3段',
		skuPhase: 2
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465491384061698',
		skuName: '臻爱倍护A2 1段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/ywky4vr5dj6mfkhqk4isl6t0gvaw0ckn.png',
		skuTypeName: '臻爱倍护A2 1段',
		skuPhase: 0
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465491396762882',
		skuName: '臻爱倍护A2 2段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/ufpr6d443c8tshdlh2mle3ir7088di7h.png',
		skuTypeName: '臻爱倍护A2 2段',
		skuPhase: 1
	},
	{
		activityId: 'fh04',
		couponId: '7489039560053014794',
		skuId: '3465322222360578',
		skuName: '臻爱倍护A2 3段',
		skuImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/05oeo5ysag0obe6dcqmpqle48wpnlb76.png',
		skuTypeName: '臻爱倍护A2 3段',
		skuPhase: 2
	}
];
const addSku = () => {
	_skuList.forEach((item) => {
		item.skuId = '';
		delete item.activityId;
		delete item.couponId;
	});
	formData.value.skuInfoList = _skuList;
};
getSkuTypeNameOptions();
getActInfo();
</script>

<style scoped lang="scss">
.add-coupon,
.add-goods {
	margin-top: 10px;
}
.width150 {
	width: 150px;
}
.table-btn {
	padding: 0 !important;
	&:last-child {
		margin-right: 0;
	}
}
</style>
<style>
.hide-input input {
	display: none !important;
}
</style>
