<template>
	<a-sub-menu :key="routesSubData.name" v-if="routesSubData.meta.menu">
		<template #icon>
			<Icon :icon="routesSubData.meta.icon" />
		</template>
		<template #title>{{ routesSubData.meta.title }}</template>
		<template v-for="item in routesSubData.children">
			<template v-if="item.children && item.children.length > 0 && item.children.filter((item) => item.meta.menu).length > 0">
				<SubMenu :routes-sub-data="item" :key="item.name" />
			</template>
			<template v-else>
				<a-menu-item :key="item.name" v-if="item.meta.menu">
					<Icon :icon="item.meta.icon" />
					<span>{{ item.meta.title }}</span>
				</a-menu-item>
			</template>
		</template>
	</a-sub-menu>
</template>
<script setup lang="ts">
import { defineProps } from 'vue';
import Icon from '/@/utils/icon';
defineProps({
	routesSubData: {
		type: Object,
		default: () => {}
	}
});
</script>
<style lang="less" scoped></style>
